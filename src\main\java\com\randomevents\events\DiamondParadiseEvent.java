package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.item.ItemEntity;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.phys.Vec3;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class DiamondParadiseEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    private static final Map<ServerPlayer, List<ItemEntity>> playerDiamondItems = new HashMap<>();
    private static boolean eventActive = false;
    
    @Override
    public String getId() {
        return "diamond_paradise";
    }
    
    @Override
    public String getName() {
        return "§b§lDiamond Paradise!";
    }
    
    @Override
    public String getDescription() {
        return "Full diamond set spawns but runs away when you try to pick it up for 30 seconds";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        eventActive = true;
        playerDiamondItems.clear();
        
        // Show message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§b§lYou've been blessed with full Diamond!")));
        }
        
        // Wait 2 seconds then spawn diamond sets
        scheduler.schedule(() -> {
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                spawnDiamondSetForPlayer(player);
            }
            
            // Start the chase effect for 30 seconds (check every 0.1 seconds for smooth movement)
            for (int i = 0; i < 300; i++) { // 300 checks over 30 seconds (every 0.1 seconds)
                scheduler.schedule(() -> {
                    if (eventActive) {
                        updateDiamondChase(server);
                    }
                }, i * 100L, TimeUnit.MILLISECONDS);
            }
            
            // End event after 30 seconds
            scheduler.schedule(() -> {
                endEvent(server);
            }, 30L, TimeUnit.SECONDS);
            
        }, 2L, TimeUnit.SECONDS);
    }
    
    private void spawnDiamondSetForPlayer(ServerPlayer player) {
        ServerLevel level = player.serverLevel();
        BlockPos playerPos = player.blockPosition();
        List<ItemEntity> diamondItems = new ArrayList<>();
        
        // Full diamond set items
        ItemStack[] diamondSet = {
            new ItemStack(Items.DIAMOND_HELMET, 1),
            new ItemStack(Items.DIAMOND_CHESTPLATE, 1),
            new ItemStack(Items.DIAMOND_LEGGINGS, 1),
            new ItemStack(Items.DIAMOND_BOOTS, 1),
            new ItemStack(Items.DIAMOND_SWORD, 1),
            new ItemStack(Items.DIAMOND_PICKAXE, 1),
            new ItemStack(Items.DIAMOND_AXE, 1),
            new ItemStack(Items.DIAMOND_SHOVEL, 1),
            new ItemStack(Items.DIAMOND_HOE, 1)
        };
        
        // Spawn items in a circle in front of the player
        for (int i = 0; i < diamondSet.length; i++) {
            double angle = (2 * Math.PI * i) / diamondSet.length; // Distribute in circle
            double radius = 3.0; // 3 blocks away
            
            double x = playerPos.getX() + Math.cos(angle) * radius;
            double z = playerPos.getZ() + Math.sin(angle) * radius;
            double y = level.getHeight(net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING, (int)x, (int)z) + 1;
            
            // Create item entity
            ItemEntity itemEntity = new ItemEntity(level, x, y, z, diamondSet[i]);
            
            // Make items not despawn and not pickupable
            itemEntity.setPickUpDelay(Integer.MAX_VALUE); // Can't be picked up
            itemEntity.setUnlimitedLifetime(); // Won't despawn
            
            // Add glowing effect
            itemEntity.setGlowingTag(true);
            
            level.addFreshEntity(itemEntity);
            diamondItems.add(itemEntity);
        }
        
        playerDiamondItems.put(player, diamondItems);
    }
    
    private void updateDiamondChase(MinecraftServer server) {
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            if (!playerDiamondItems.containsKey(player)) continue;

            List<ItemEntity> items = playerDiamondItems.get(player);
            Vec3 playerPos = player.position();
            Vec3 playerVelocity = player.getDeltaMovement();

            for (ItemEntity item : items) {
                if (item.isAlive()) {
                    Vec3 itemPos = item.position();
                    double distance = playerPos.distanceTo(itemPos);

                    // If player is within 6 blocks, make item run away FAST
                    if (distance < 6.0) {
                        // Calculate direction away from player
                        double dx = itemPos.x - playerPos.x;
                        double dz = itemPos.z - playerPos.z;
                        double length = Math.sqrt(dx * dx + dz * dz);

                        if (length > 0.1) { // Avoid division by zero
                            // Normalize direction away from player
                            dx = dx / length;
                            dz = dz / length;

                            // Predict where player is going and run away from that too
                            double predictedPlayerX = playerPos.x + playerVelocity.x * 5; // Predict 5 ticks ahead
                            double predictedPlayerZ = playerPos.z + playerVelocity.z * 5;

                            double predDx = itemPos.x - predictedPlayerX;
                            double predDz = itemPos.z - predictedPlayerZ;
                            double predLength = Math.sqrt(predDx * predDx + predDz * predDz);

                            if (predLength > 0.1) {
                                predDx = predDx / predLength;
                                predDz = predDz / predLength;

                                // Combine current escape direction with predicted escape direction
                                dx = (dx * 0.7 + predDx * 0.3);
                                dz = (dz * 0.7 + predDz * 0.3);
                            }

                            // Apply FAST escape speed based on how close player is
                            double escapeSpeed = 1.2 - (distance / 6.0) * 0.8; // Faster when closer (0.4 to 1.2)
                            dx *= escapeSpeed;
                            dz *= escapeSpeed;

                            // Add some erratic movement to make it harder to catch
                            dx += (random.nextDouble() - 0.5) * 0.4;
                            dz += (random.nextDouble() - 0.5) * 0.4;

                            // Apply movement away from player with upward bounce
                            item.setDeltaMovement(dx, 0.3, dz);
                        } else {
                            // If too close, teleport away immediately
                            double angle = random.nextDouble() * 2 * Math.PI;
                            double newX = playerPos.x + Math.cos(angle) * 4; // 4 blocks away
                            double newZ = playerPos.z + Math.sin(angle) * 4;
                            double newY = player.serverLevel().getHeight(
                                net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING,
                                (int)newX, (int)newZ) + 1;

                            item.teleportTo(newX, newY, newZ);
                        }

                        // Make sure item doesn't go too far (max 12 blocks from player)
                        if (distance > 12.0) {
                            // Teleport back closer to player if too far
                            double angle = random.nextDouble() * 2 * Math.PI;
                            double newX = playerPos.x + Math.cos(angle) * 6; // 6 blocks away
                            double newZ = playerPos.z + Math.sin(angle) * 6;
                            double newY = player.serverLevel().getHeight(
                                net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING,
                                (int)newX, (int)newZ) + 1;

                            item.teleportTo(newX, newY, newZ);
                        }
                    } else {
                        // If player is far away, items slowly drift back toward player (but not too close)
                        if (distance > 8.0) {
                            double dx = playerPos.x - itemPos.x;
                            double dz = playerPos.z - itemPos.z;
                            double length = Math.sqrt(dx * dx + dz * dz);

                            if (length > 0) {
                                dx = (dx / length) * 0.1; // Slow drift back
                                dz = (dz / length) * 0.1;

                                item.setDeltaMovement(dx, 0, dz);
                            }
                        } else {
                            // In the "sweet spot" - just hover and taunt the player
                            item.setDeltaMovement(
                                (random.nextDouble() - 0.5) * 0.1,
                                Math.sin(System.currentTimeMillis() / 1000.0) * 0.05, // Gentle bobbing
                                (random.nextDouble() - 0.5) * 0.1
                            );
                        }
                    }
                }
            }
        }
    }
    
    private void endEvent(MinecraftServer server) {
        eventActive = false;
        
        // Remove all diamond items
        for (List<ItemEntity> items : playerDiamondItems.values()) {
            for (ItemEntity item : items) {
                if (item.isAlive()) {
                    item.discard();
                }
            }
        }
        
        // Show end message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§7§lThe diamond paradise fades away... §8It was all a mirage!")));
        }
        
        playerDiamondItems.clear();
    }
}
