package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.item.ItemEntity;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.phys.Vec3;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class DiamondParadiseEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    private static final Map<ServerPlayer, List<ItemEntity>> playerDiamondItems = new HashMap<>();
    private static boolean eventActive = false;
    
    @Override
    public String getId() {
        return "diamond_paradise";
    }
    
    @Override
    public String getName() {
        return "§b§lDiamond Paradise!";
    }
    
    @Override
    public String getDescription() {
        return "Full diamond set spawns but runs away when you try to pick it up for 30 seconds";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        eventActive = true;
        playerDiamondItems.clear();
        
        // Show message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§b§lYou've been blessed with full Diamond!")));
        }
        
        // Wait 2 seconds then spawn diamond sets
        scheduler.schedule(() -> {
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                spawnDiamondSetForPlayer(player);
            }
            
            // Start the chase effect for 30 seconds (check every 0.5 seconds)
            for (int i = 0; i < 60; i++) { // 60 checks over 30 seconds
                scheduler.schedule(() -> {
                    if (eventActive) {
                        updateDiamondChase(server);
                    }
                }, i * 500L, TimeUnit.MILLISECONDS);
            }
            
            // End event after 30 seconds
            scheduler.schedule(() -> {
                endEvent(server);
            }, 30L, TimeUnit.SECONDS);
            
        }, 2L, TimeUnit.SECONDS);
    }
    
    private void spawnDiamondSetForPlayer(ServerPlayer player) {
        ServerLevel level = player.serverLevel();
        BlockPos playerPos = player.blockPosition();
        List<ItemEntity> diamondItems = new ArrayList<>();
        
        // Full diamond set items
        ItemStack[] diamondSet = {
            new ItemStack(Items.DIAMOND_HELMET, 1),
            new ItemStack(Items.DIAMOND_CHESTPLATE, 1),
            new ItemStack(Items.DIAMOND_LEGGINGS, 1),
            new ItemStack(Items.DIAMOND_BOOTS, 1),
            new ItemStack(Items.DIAMOND_SWORD, 1),
            new ItemStack(Items.DIAMOND_PICKAXE, 1),
            new ItemStack(Items.DIAMOND_AXE, 1),
            new ItemStack(Items.DIAMOND_SHOVEL, 1),
            new ItemStack(Items.DIAMOND_HOE, 1)
        };
        
        // Spawn items in a circle in front of the player
        for (int i = 0; i < diamondSet.length; i++) {
            double angle = (2 * Math.PI * i) / diamondSet.length; // Distribute in circle
            double radius = 3.0; // 3 blocks away
            
            double x = playerPos.getX() + Math.cos(angle) * radius;
            double z = playerPos.getZ() + Math.sin(angle) * radius;
            double y = level.getHeight(net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING, (int)x, (int)z) + 1;
            
            // Create item entity
            ItemEntity itemEntity = new ItemEntity(level, x, y, z, diamondSet[i]);
            
            // Make items not despawn and not pickupable
            itemEntity.setPickUpDelay(Integer.MAX_VALUE); // Can't be picked up
            itemEntity.setUnlimitedLifetime(); // Won't despawn
            
            // Add glowing effect
            itemEntity.setGlowingTag(true);
            
            level.addFreshEntity(itemEntity);
            diamondItems.add(itemEntity);
        }
        
        playerDiamondItems.put(player, diamondItems);
    }
    
    private void updateDiamondChase(MinecraftServer server) {
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            if (!playerDiamondItems.containsKey(player)) continue;
            
            List<ItemEntity> items = playerDiamondItems.get(player);
            Vec3 playerPos = player.position();
            
            for (ItemEntity item : items) {
                if (item.isAlive()) {
                    Vec3 itemPos = item.position();
                    double distance = playerPos.distanceTo(itemPos);
                    
                    // If player is within 4 blocks, make item run away
                    if (distance < 4.0) {
                        // Calculate direction away from player
                        double dx = itemPos.x - playerPos.x;
                        double dz = itemPos.z - playerPos.z;
                        double length = Math.sqrt(dx * dx + dz * dz);
                        
                        if (length > 0) {
                            // Normalize and apply escape force
                            dx = (dx / length) * 0.3; // Escape speed
                            dz = (dz / length) * 0.3;
                            
                            // Add some randomness to make it more chaotic
                            dx += (random.nextDouble() - 0.5) * 0.2;
                            dz += (random.nextDouble() - 0.5) * 0.2;
                            
                            // Apply movement away from player
                            item.setDeltaMovement(dx, 0.1, dz); // Small upward movement too
                            
                            // Make sure item doesn't go too far (max 15 blocks from original spawn)
                            if (distance > 15.0) {
                                // Teleport back closer to player if too far
                                double angle = random.nextDouble() * 2 * Math.PI;
                                double newX = playerPos.x + Math.cos(angle) * 8; // 8 blocks away
                                double newZ = playerPos.z + Math.sin(angle) * 8;
                                double newY = player.serverLevel().getHeight(
                                    net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING, 
                                    (int)newX, (int)newZ) + 1;
                                
                                item.teleportTo(newX, newY, newZ);
                            }
                        }
                    } else {
                        // If player is far away, items slowly drift back toward player
                        if (distance > 8.0) {
                            double dx = playerPos.x - itemPos.x;
                            double dz = playerPos.z - itemPos.z;
                            double length = Math.sqrt(dx * dx + dz * dz);
                            
                            if (length > 0) {
                                dx = (dx / length) * 0.05; // Slow drift back
                                dz = (dz / length) * 0.05;
                                
                                item.setDeltaMovement(dx, 0, dz);
                            }
                        }
                    }
                }
            }
        }
    }
    
    private void endEvent(MinecraftServer server) {
        eventActive = false;
        
        // Remove all diamond items
        for (List<ItemEntity> items : playerDiamondItems.values()) {
            for (ItemEntity item : items) {
                if (item.isAlive()) {
                    item.discard();
                }
            }
        }
        
        // Show end message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§7§lThe diamond paradise fades away... §8It was all a mirage!")));
        }
        
        playerDiamondItems.clear();
    }
}
