package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.monster.Blaze;
import net.minecraft.world.entity.monster.Ghast;
import net.minecraft.world.entity.monster.WitherSkeleton;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;

import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class NetherInvasionEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    private static final Map<ServerPlayer, List<BlockPos>> playerPortals = new HashMap<>();
    private static final List<BlockPos> allPortals = new ArrayList<>();
    private static final Set<ServerPlayer> playersWhoEnteredPortal = new HashSet<>();
    private static boolean eventActive = false;

    @Override
    public String getId() {
        return "nether_invasion";
    }

    @Override
    public String getName() {
        return "§4§lNether Invasion!";
    }

    @Override
    public String getDescription() {
        return "Nether portals spawn around players - enter within 12 seconds or be eliminated!";
    }

    @Override
    public boolean canTrigger(MinecraftServer server) {
        // Only allow this event to trigger on event 80 or higher
        com.randomevents.manager.RandomEventManager manager = com.randomevents.RandomEventsMod.getEventManager();
        if (manager != null) {
            int currentEventNumber = manager.getCurrentEventNumber();
            return currentEventNumber >= 80;
        }
        return false; // Don't trigger if we can't get the event manager
    }

    @Override
    public void execute(MinecraftServer server) {
        eventActive = true;
        playerPortals.clear();
        allPortals.clear();
        playersWhoEnteredPortal.clear();
        
        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§4§lThe Nether is breaking through...")));
        }

        // Wait 2 seconds then spawn portals
        scheduler.schedule(() -> {
            ServerLevel overworld = server.overworld();
            
            // Spawn MANY nether portals around each player (5-8 portals per player)
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                int portalCount = 5 + random.nextInt(4); // 5-8 portals per player
                List<BlockPos> playerPortalList = new ArrayList<>();

                for (int i = 0; i < portalCount; i++) {
                    BlockPos portalPos = createNetherPortalNearPlayer(overworld, player);
                    if (portalPos != null) {
                        // Store portal for this player
                        playerPortalList.add(portalPos);
                        // Also store in global list for easy access
                        allPortals.add(portalPos);
                    }
                }

                // Store the list of portals for this player
                if (!playerPortalList.isEmpty()) {
                    playerPortals.put(player, playerPortalList);
                }
            }

            // Show urgent message
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetTitleTextPacket(
                    net.minecraft.network.chat.Component.literal("§4§lENTER ANY PORTAL!")));
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetSubtitleTextPacket(
                    net.minecraft.network.chat.Component.literal("§cMultiple portals spawned! 12 seconds or elimination!")));
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetTitlesAnimationPacket(
                    0, 240, 0)); // Stay for 12 seconds
            }

            // Play dramatic sound
            overworld.playSound(null, BlockPos.ZERO, SoundEvents.WITHER_SPAWN, SoundSource.HOSTILE, 2.0f, 0.5f);

            // Start countdown and elimination timer
            startCountdownAndElimination(server);

        }, 2L, TimeUnit.SECONDS);
    }

    private BlockPos createNetherPortalNearPlayer(ServerLevel level, ServerPlayer player) {
        BlockPos playerPos = player.blockPosition();
        
        // Find a suitable location 5-25 blocks away (wider spread for more portals)
        for (int attempts = 0; attempts < 30; attempts++) {
            int distance = 5 + random.nextInt(21); // 5-25 blocks away
            double angle = random.nextDouble() * 2 * Math.PI;
            int x = playerPos.getX() + (int)(Math.cos(angle) * distance);
            int z = playerPos.getZ() + (int)(Math.sin(angle) * distance);
            int y = level.getHeight(net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING, x, z);
            
            BlockPos portalBase = new BlockPos(x, y, z);
            
            // Check if area is clear
            boolean canPlace = true;
            for (int dx = -1; dx <= 1; dx++) {
                for (int dy = 0; dy <= 4; dy++) {
                    for (int dz = -1; dz <= 1; dz++) {
                        BlockPos checkPos = portalBase.offset(dx, dy, dz);
                        if (!level.getBlockState(checkPos).isAir() && dy > 0) {
                            canPlace = false;
                            break;
                        }
                    }
                    if (!canPlace) break;
                }
                if (!canPlace) break;
            }
            
            if (canPlace) {
                // Create the nether portal frame
                createPortalFrame(level, portalBase);
                
                // Light the portal
                level.setBlock(portalBase.offset(0, 1, 0), Blocks.NETHER_PORTAL.defaultBlockState(), 3);
                level.setBlock(portalBase.offset(0, 2, 0), Blocks.NETHER_PORTAL.defaultBlockState(), 3);
                
                // Play portal sound
                level.playSound(null, portalBase, SoundEvents.PORTAL_AMBIENT, SoundSource.BLOCKS, 1.0f, 1.0f);
                
                return portalBase;
            }
        }
        return null;
    }

    private void createPortalFrame(ServerLevel level, BlockPos base) {
        // Create obsidian frame for nether portal
        BlockState obsidian = Blocks.OBSIDIAN.defaultBlockState();
        
        // Bottom frame
        level.setBlock(base.offset(-1, 0, 0), obsidian, 3);
        level.setBlock(base.offset(0, 0, 0), obsidian, 3);
        level.setBlock(base.offset(1, 0, 0), obsidian, 3);
        
        // Side frames
        level.setBlock(base.offset(-1, 1, 0), obsidian, 3);
        level.setBlock(base.offset(-1, 2, 0), obsidian, 3);
        level.setBlock(base.offset(-1, 3, 0), obsidian, 3);
        level.setBlock(base.offset(1, 1, 0), obsidian, 3);
        level.setBlock(base.offset(1, 2, 0), obsidian, 3);
        level.setBlock(base.offset(1, 3, 0), obsidian, 3);
        
        // Top frame
        level.setBlock(base.offset(-1, 4, 0), obsidian, 3);
        level.setBlock(base.offset(0, 4, 0), obsidian, 3);
        level.setBlock(base.offset(1, 4, 0), obsidian, 3);
    }

    private void startCountdownAndElimination(MinecraftServer server) {
        // Countdown from 10 to 1
        for (int i = 10; i >= 1; i--) {
            final int count = i;
            scheduler.schedule(() -> {
                if (!eventActive) return;
                
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    if (!playersWhoEnteredPortal.contains(player)) {
                        player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                            net.minecraft.network.chat.Component.literal("§c§l" + count + " seconds remaining!")));
                    }
                }
                
                // Play countdown sound
                server.overworld().playSound(null, BlockPos.ZERO, SoundEvents.ANVIL_LAND, SoundSource.BLOCKS, 1.0f, 2.0f);
                
            }, (12 - i) * 1000L, TimeUnit.MILLISECONDS);
        }

        // Elimination after 12 seconds
        scheduler.schedule(() -> {
            if (!eventActive) return;
            
            List<ServerPlayer> playersToEliminate = new ArrayList<>();
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                if (!playersWhoEnteredPortal.contains(player)) {
                    playersToEliminate.add(player);
                }
            }

            // Eliminate players who didn't enter portals
            for (ServerPlayer player : playersToEliminate) {
                // Show elimination message
                for (ServerPlayer allPlayer : server.getPlayerList().getPlayers()) {
                    allPlayer.connection.send(new net.minecraft.network.protocol.game.ClientboundSetTitleTextPacket(
                        net.minecraft.network.chat.Component.literal("§4§l" + player.getName().getString() + " was consumed by the Nether!")));
                    allPlayer.connection.send(new net.minecraft.network.protocol.game.ClientboundSetTitlesAnimationPacket(
                        0, 60, 20)); // Stay 3s, fade out 1s
                }

                // Kill the player
                player.hurt(server.overworld().damageSources().magic(), 1000.0f);
                
                // Create dramatic explosion
                BlockPos playerPos = player.blockPosition();
                server.overworld().explode(null, playerPos.getX(), playerPos.getY(), playerPos.getZ(), 
                    3.0f, false, net.minecraft.world.level.Level.ExplosionInteraction.NONE);
            }

            // Spawn nether mobs from portals
            spawnNetherMobs(server);

            // Clean up after 2 minutes
            scheduler.schedule(() -> {
                cleanupEvent(server);
            }, 120L, TimeUnit.SECONDS);

        }, 12L, TimeUnit.SECONDS);
    }

    private void spawnNetherMobs(MinecraftServer server) {
        ServerLevel overworld = server.overworld();

        for (BlockPos portalPos : allPortals) {
            // Spawn 3-5 nether mobs per portal
            int mobCount = 3 + random.nextInt(3);
            for (int i = 0; i < mobCount; i++) {
                scheduler.schedule(() -> {
                    BlockPos spawnPos = portalPos.offset(
                        random.nextInt(6) - 3, 
                        1, 
                        random.nextInt(6) - 3
                    );
                    
                    // Randomly choose mob type
                    int mobType = random.nextInt(3);
                    try {
                        switch (mobType) {
                            case 0: // Ghast
                                Ghast ghast = new Ghast(EntityType.GHAST, overworld);
                                ghast.moveTo(spawnPos.getX(), spawnPos.getY() + 5, spawnPos.getZ());
                                overworld.addFreshEntity(ghast);
                                break;
                            case 1: // Blaze
                                Blaze blaze = new Blaze(EntityType.BLAZE, overworld);
                                blaze.moveTo(spawnPos.getX(), spawnPos.getY(), spawnPos.getZ());
                                overworld.addFreshEntity(blaze);
                                break;
                            case 2: // Wither Skeleton
                                WitherSkeleton witherSkeleton = new WitherSkeleton(EntityType.WITHER_SKELETON, overworld);
                                witherSkeleton.moveTo(spawnPos.getX(), spawnPos.getY(), spawnPos.getZ());
                                overworld.addFreshEntity(witherSkeleton);
                                break;
                        }
                    } catch (Exception e) {
                        // Silently handle mob spawn errors
                    }
                    
                }, i * 2000L, TimeUnit.MILLISECONDS); // Spawn every 2 seconds
            }
        }
    }

    private void cleanupEvent(MinecraftServer server) {
        eventActive = false;
        
        // Remove portals
        ServerLevel overworld = server.overworld();
        for (BlockPos portalPos : allPortals) {
            // Remove portal blocks in 3x5 area
            for (int dx = -1; dx <= 1; dx++) {
                for (int dy = 0; dy <= 4; dy++) {
                    BlockPos removePos = portalPos.offset(dx, dy, 0);
                    BlockState state = overworld.getBlockState(removePos);
                    if (state.is(Blocks.NETHER_PORTAL) || state.is(Blocks.OBSIDIAN)) {
                        overworld.setBlock(removePos, Blocks.AIR.defaultBlockState(), 3);
                    }
                }
            }
        }

        // Clear tracking data
        playerPortals.clear();
        allPortals.clear();
        playersWhoEnteredPortal.clear();
        
        // Show cleanup message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§7§lThe Nether invasion ends... §8The portals close.")));
        }
    }

    // This method should be called when a player enters a nether portal
    public static void onPlayerEnterPortal(ServerPlayer player) {
        if (eventActive && (playerPortals.containsKey(player) || allPortals.contains(player.blockPosition()))) {
            playersWhoEnteredPortal.add(player);
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§a§lYou escaped the Nether invasion!")));
        }
    }
}
