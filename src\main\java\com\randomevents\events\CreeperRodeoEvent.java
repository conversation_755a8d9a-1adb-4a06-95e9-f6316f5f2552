package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.monster.Creeper;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class CreeperRodeoEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Map<ServerPlayer, Creeper> playerCreepers = new HashMap<>();
    private static final Map<ServerPlayer, Boolean> playerWasRiding = new HashMap<>(); // Track if player was riding
    private static final Random random = new Random();
    private static boolean rodeoActive = false;
    
    @Override
    public String getId() {
        return "creeper_rodeo";
    }
    
    @Override
    public String getName() {
        return "§2§lCreeper Rodeo!";
    }
    
    @Override
    public String getDescription() {
        return "You're forced to ride a creeper for 10 seconds. If it explodes, you lose!";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        rodeoActive = true;
        playerCreepers.clear();
        
        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§2§lGET READY FOR THE CREEPER RODEO! §7Hold on tight!")));
        }
        
        // Wait 3 seconds then start the rodeo
        scheduler.schedule(() -> {
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                ServerLevel level = player.serverLevel();
                BlockPos playerPos = player.blockPosition();
                
                // Spawn a creeper for each player
                // Create a regular creeper but configure it to not explode
                Creeper creeper = EntityType.CREEPER.create(level);

                if (creeper != null) {
                    // Position creeper at player location
                    creeper.moveTo(playerPos.getX() + 0.5, playerPos.getY(), playerPos.getZ() + 0.5);

                    // Make creeper not target players
                    creeper.setTarget(null);

                    // Make creeper invulnerable so players can't kill it
                    creeper.setInvulnerable(true);

                    // Configure creeper to prevent explosions
                    net.minecraft.nbt.CompoundTag nbt = new net.minecraft.nbt.CompoundTag();
                    creeper.saveWithoutId(nbt);
                    nbt.putBoolean("RodeoCreeper", true); // Custom tag to identify rodeo creepers
                    nbt.putInt("ExplosionRadius", 0); // No explosion radius
                    nbt.putBoolean("powered", false); // Not powered (charged)
                    nbt.putShort("Fuse", (short) 30); // Set fuse to 30 (1.5 seconds) but we'll reset it constantly
                    nbt.putBoolean("ignited", false); // Make sure it's not ignited
                    creeper.load(nbt);

                    // Disable AI to prevent normal creeper behavior
                    creeper.setNoAi(true);

                    // Start a task to constantly reset the fuse to prevent explosion
                    startFuseResetTask(creeper);
                    
                    // Add creeper to world
                    level.addFreshEntity(creeper);
                    
                    // Force player to ride the creeper
                    player.startRiding(creeper, true);
                    
                    // Store the creeper for this player
                    playerCreepers.put(player, creeper);
                    playerWasRiding.put(player, true); // Mark as riding

                    // Show rodeo start message
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                        net.minecraft.network.chat.Component.literal("§2§lCREEPER RODEO STARTED! §7Stay on for 10 seconds!")));

                    // Play creeper hiss sound
                    level.playSound(null, playerPos, SoundEvents.CREEPER_PRIMED, SoundSource.HOSTILE, 1.0f, 1.5f);

                    // Start erratic movement for this creeper
                    startErraticMovement(creeper, level);
                }
            }
            
            // Start countdown warnings
            for (int i = 1; i <= 9; i++) {
                final int timeLeft = 10 - i;
                scheduler.schedule(() -> {
                    if (rodeoActive) {
                        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                            if (playerCreepers.containsKey(player) && player.getVehicle() == playerCreepers.get(player)) {
                                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                                    net.minecraft.network.chat.Component.literal("§2§l" + timeLeft + " SECONDS LEFT! §7Hold on!")));
                            }
                        }
                    }
                }, i * 1L, TimeUnit.SECONDS);
            }
            
            // Check every 0.5 seconds if players are still riding (but don't explode them)
            for (int i = 0; i < 20; i++) { // 20 checks over 10 seconds
                scheduler.schedule(() -> {
                    if (rodeoActive) {
                        checkRodeoStatus(server);
                    }
                }, i * 500L, TimeUnit.MILLISECONDS);
            }
            
            // End rodeo after 10 seconds
            scheduler.schedule(() -> {
                endRodeo(server, true); // true = successful completion
            }, 10L, TimeUnit.SECONDS);
            
        }, 3L, TimeUnit.SECONDS);
    }
    
    private void checkRodeoStatus(MinecraftServer server) {
        // Create a copy of the entry set to avoid concurrent modification
        Map<ServerPlayer, Creeper> currentCreepers = new HashMap<>(playerCreepers);

        for (Map.Entry<ServerPlayer, Creeper> entry : currentCreepers.entrySet()) {
            ServerPlayer player = entry.getKey();
            Creeper creeper = entry.getValue();
            boolean wasRiding = playerWasRiding.getOrDefault(player, false);

            // Check current riding status
            boolean isCurrentlyRiding = (player.getVehicle() == creeper);

            // If player was riding but is no longer riding, they dismounted
            if (wasRiding && !isCurrentlyRiding) {
                // Player just dismounted - check if they used shift
                if (player.isCrouching() || player.isShiftKeyDown()) {
                    // Player dismounted with shift - make creeper explode!
                    explodeCreeper(player, creeper);
                } else {
                    // Player fell off naturally - show failure message but DON'T explode
                    showFailureMessage(player, creeper);
                }
                // Remove from tracking since they're no longer riding
                playerWasRiding.remove(player);
            } else if (isCurrentlyRiding) {
                // Update riding status
                playerWasRiding.put(player, true);
            }
        }
    }

    private void startFuseResetTask(Creeper creeper) {
        // Reset the creeper's fuse every 0.1 seconds to prevent explosion
        for (int i = 0; i < 100; i++) { // 100 resets over 10 seconds
            scheduler.schedule(() -> {
                if (creeper.isAlive() && rodeoActive) {
                    // Reset fuse to prevent explosion
                    net.minecraft.nbt.CompoundTag nbt = new net.minecraft.nbt.CompoundTag();
                    creeper.saveWithoutId(nbt);
                    nbt.putShort("Fuse", (short) 30); // Reset fuse to 30 (1.5 seconds)
                    nbt.putBoolean("ignited", false); // Make sure it's not ignited
                    creeper.load(nbt);
                }
            }, i * 100L, TimeUnit.MILLISECONDS); // Every 0.1 seconds
        }
    }

    private void startErraticMovement(Creeper creeper, ServerLevel level) {
        // Make creeper run around actively every 0.2 seconds for 10 seconds
        for (int i = 0; i < 50; i++) { // 50 movements over 10 seconds (every 0.2 seconds)
            scheduler.schedule(() -> {
                if (creeper.isAlive() && rodeoActive) {
                    // Random movement direction - more active running
                    double angle = random.nextDouble() * 2 * Math.PI;
                    double speed = 0.8 + random.nextDouble() * 0.7; // 0.8-1.5 speed (much faster)

                    double velocityX = Math.cos(angle) * speed;
                    double velocityZ = Math.sin(angle) * speed;
                    double velocityY = 0.2 + random.nextDouble() * 0.3; // Bigger jumps

                    // Set velocity directly for more responsive movement
                    creeper.setDeltaMovement(velocityX, velocityY, velocityZ);
                    creeper.hurtMarked = true; // Force velocity update

                    // Play sizzling sound more often
                    if (random.nextInt(2) == 0) { // 50% chance
                        level.playSound(null, creeper.blockPosition(), SoundEvents.CREEPER_PRIMED, SoundSource.HOSTILE, 0.7f, 1.0f + random.nextFloat());
                    }
                }
            }, i * 200L, TimeUnit.MILLISECONDS); // Every 0.2 seconds
        }
    }
    
    private void explodeCreeper(ServerPlayer player, Creeper creeper) {
        if (!playerCreepers.containsKey(player)) return; // Already handled

        // Remove from tracking
        playerCreepers.remove(player);

        // Show explosion message
        player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
            net.minecraft.network.chat.Component.literal("§c§lYOU DISMOUNTED! §4§lCREEPER EXPLODES!")));

        // Make the creeper explode at its current location
        ServerLevel level = (ServerLevel) creeper.level();
        BlockPos creeperPos = creeper.blockPosition();

        // Remove the creeper first
        creeper.remove(net.minecraft.world.entity.Entity.RemovalReason.DISCARDED);

        // Create explosion (reduced damage)
        level.explode(null, creeperPos.getX(), creeperPos.getY(), creeperPos.getZ(),
                     2.0f, false, net.minecraft.world.level.Level.ExplosionInteraction.TNT);

        // Deal 1 heart of damage to the player
        player.hurt(level.damageSources().explosion(null, null), 2.0f);
    }

    private void showFailureMessage(ServerPlayer player, Creeper creeper) {
        if (!playerCreepers.containsKey(player)) return; // Already handled

        // Remove from tracking
        playerCreepers.remove(player);

        // Show failure message but NO explosion
        player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
            net.minecraft.network.chat.Component.literal("§c§lYOU FELL OFF THE CREEPER! §7But it doesn't explode...")));

        // Keep the creeper alive and sizzling - don't remove it
        // The creeper will be cleaned up when the event ends
    }
    
    private void endRodeo(MinecraftServer server, boolean successful) {
        rodeoActive = false;
        
        // Handle remaining players
        for (Map.Entry<ServerPlayer, Creeper> entry : playerCreepers.entrySet()) {
            ServerPlayer player = entry.getKey();
            Creeper creeper = entry.getValue();
            
            // Stop riding
            player.stopRiding();
            
            if (successful && player.getVehicle() == creeper) {
                // Player successfully completed the rodeo
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§a§lRODEO COMPLETE! §7You survived the creeper ride!")));
                
                // Play success sound
                ServerLevel level = player.serverLevel();
                level.playSound(null, player.blockPosition(), SoundEvents.PLAYER_LEVELUP, SoundSource.PLAYERS, 1.0f, 1.0f);
            }
            
            // Remove the creeper safely
            creeper.remove(net.minecraft.world.entity.Entity.RemovalReason.DISCARDED);
        }
        
        // Clear tracking
        playerCreepers.clear();
        playerWasRiding.clear();
        
        // Final message to all players
        scheduler.schedule(() -> {
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§7§lThe creeper rodeo has ended.")));
            }
        }, 2L, TimeUnit.SECONDS);
    }
}
