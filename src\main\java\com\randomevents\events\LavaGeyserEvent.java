package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;

import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class LavaGeyserEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    private static final Map<BlockPos, BlockState> originalBlocks = new HashMap<>();
    private static final List<BlockPos> activeGeysers = new ArrayList<>();
    
    @Override
    public String getId() {
        return "lava_geyser";
    }
    
    @Override
    public String getName() {
        return "§c§lLava Geysers!";
    }
    
    @Override
    public String getDescription() {
        return "<PERSON><PERSON> shoots up from the ground in towering geysers for 30 seconds";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        originalBlocks.clear();
        activeGeysers.clear();
        
        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§c§lThe ground is rumbling... §6§lLava geysers are forming!")));
        }
        
        // Wait 3 seconds then start the geysers
        scheduler.schedule(() -> {
            // Create geysers every 2-3 seconds for 30 seconds
            for (int i = 0; i < 12; i++) { // 12 geysers over 30 seconds
                scheduler.schedule(() -> {
                    for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                        // 60% chance to create a geyser near each player
                        if (random.nextInt(10) < 6) {
                            createLavaGeyser(player);
                        }
                    }
                }, i * (2000L + random.nextInt(1000)), TimeUnit.MILLISECONDS); // Every 2-3 seconds
            }

            // Clean up after 30 seconds
            scheduler.schedule(() -> {
                // Restore original blocks after cleanup
                scheduler.schedule(() -> {
                    restoreOriginalBlocks();
                }, 5L, TimeUnit.SECONDS);

            }, 30L, TimeUnit.SECONDS);

        }, 3L, TimeUnit.SECONDS); // 3 second delay after warning
    }
    
    private void createLavaGeyser(ServerPlayer player) {
        ServerLevel level = player.serverLevel();
        BlockPos playerPos = player.blockPosition();
        
        // Choose a random location 5-15 blocks away from the player
        int distance = 5 + random.nextInt(11); // 5-15 blocks
        double angle = random.nextDouble() * 2 * Math.PI;
        
        int geyserX = playerPos.getX() + (int)(Math.cos(angle) * distance);
        int geyserZ = playerPos.getZ() + (int)(Math.sin(angle) * distance);
        
        // Find the ground level
        int groundY = level.getHeight(net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING_NO_LEAVES, geyserX, geyserZ);
        BlockPos geyserBase = new BlockPos(geyserX, groundY, geyserZ);
        
        // Don't create geysers too close to existing ones
        for (BlockPos existingGeyser : activeGeysers) {
            if (existingGeyser.distSqr(geyserBase) < 25) { // Within 5 blocks
                return; // Skip this geyser
            }
        }
        
        // Create the geyser
        createGeyserColumn(level, geyserBase, player);
        activeGeysers.add(geyserBase);
        
        // Remove geyser from active list after 8-12 seconds
        int geyserDuration = 8000 + random.nextInt(4000); // 8-12 seconds
        scheduler.schedule(() -> {
            activeGeysers.remove(geyserBase);
        }, geyserDuration, TimeUnit.MILLISECONDS);
    }
    
    private void createGeyserColumn(ServerLevel level, BlockPos base, ServerPlayer nearbyPlayer) {
        // Determine geyser height (8-15 blocks high)
        int height = 8 + random.nextInt(8);
        
        // Play eruption sound
        level.playSound(null, base, SoundEvents.LAVA_POP, SoundSource.BLOCKS, 2.0f, 0.5f);
        level.playSound(null, base, SoundEvents.GENERIC_EXPLODE, SoundSource.BLOCKS, 1.0f, 1.5f);
        
        // Create the geyser column with animation
        for (int y = 0; y < height; y++) {
            final int currentY = y;
            final BlockPos geyserPos = base.above(currentY);
            
            // Animate the geyser shooting up (delay each block)
            scheduler.schedule(() -> {
                if (level.isInWorldBounds(geyserPos)) {
                    BlockState currentState = level.getBlockState(geyserPos);
                    
                    // Store original block if not already stored
                    if (!originalBlocks.containsKey(geyserPos) && !currentState.isAir()) {
                        originalBlocks.put(geyserPos, currentState);
                    }
                    
                    // Place lava block with reduced tick updates
                    level.setBlock(geyserPos, Blocks.LAVA.defaultBlockState(), 2);
                    
                    // Play lava sound for each block
                    if (random.nextInt(3) == 0) { // 33% chance
                        level.playSound(null, geyserPos, SoundEvents.LAVA_AMBIENT, SoundSource.BLOCKS, 0.5f, 1.0f + random.nextFloat() * 0.5f);
                    }
                    
                    // Create some particle effects by placing and removing fire blocks
                    for (int dx = -1; dx <= 1; dx++) {
                        for (int dz = -1; dz <= 1; dz++) {
                            if (dx == 0 && dz == 0) continue;
                            
                            BlockPos firePos = geyserPos.offset(dx, 0, dz);
                            if (level.getBlockState(firePos).isAir() && random.nextInt(3) == 0) {
                                // Temporarily place fire for effect
                                level.setBlock(firePos, Blocks.FIRE.defaultBlockState(), 3);
                                
                                // Remove fire after 1-3 seconds
                                scheduler.schedule(() -> {
                                    if (level.getBlockState(firePos).is(Blocks.FIRE)) {
                                        level.setBlock(firePos, Blocks.AIR.defaultBlockState(), 3);
                                    }
                                }, 1000L + random.nextInt(2000), TimeUnit.MILLISECONDS);
                            }
                        }
                    }
                }
            }, currentY * 100L, TimeUnit.MILLISECONDS); // 100ms delay between each block
        }
        
        // No individual geyser warning messages
        
        // Start removing the geyser from top to bottom after 6-10 seconds
        int removalDelay = 6000 + random.nextInt(4000); // 6-10 seconds
        scheduler.schedule(() -> {
            removeGeyserColumn(level, base, height);
        }, removalDelay, TimeUnit.MILLISECONDS);
    }
    
    private void removeGeyserColumn(ServerLevel level, BlockPos base, int height) {
        // Remove geyser from top to bottom with animation
        for (int y = height - 1; y >= 0; y--) {
            final BlockPos geyserPos = base.above(y);
            
            scheduler.schedule(() -> {
                if (level.isInWorldBounds(geyserPos) && level.getBlockState(geyserPos).is(Blocks.LAVA)) {
                    // Check if we have an original block to restore
                    if (originalBlocks.containsKey(geyserPos)) {
                        level.setBlock(geyserPos, originalBlocks.get(geyserPos), 3);
                    } else {
                        level.setBlock(geyserPos, Blocks.AIR.defaultBlockState(), 3);
                    }
                    
                    // Play cooling sound
                    if (random.nextInt(4) == 0) { // 25% chance
                        level.playSound(null, geyserPos, SoundEvents.LAVA_EXTINGUISH, SoundSource.BLOCKS, 0.3f, 1.0f);
                    }
                }
            }, (height - 1 - y) * 150L, TimeUnit.MILLISECONDS); // 150ms delay between each block removal
        }
    }
    
    private void restoreOriginalBlocks() {
        // Final cleanup - restore any remaining changed blocks
        for (Map.Entry<BlockPos, BlockState> entry : originalBlocks.entrySet()) {
            BlockPos pos = entry.getKey();
            BlockState originalState = entry.getValue();
            
            // Find the level (assuming overworld for cleanup)
            // Note: In a real implementation, you'd want to track which level each block belongs to
            // For now, we'll skip this final cleanup as the individual geyser cleanup should handle most cases
        }
        
        originalBlocks.clear();
        activeGeysers.clear();
    }
}
