package com.randomevents.commands;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.IntegerArgumentType;
import com.mojang.brigadier.arguments.StringArgumentType;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.suggestion.SuggestionProvider;
import com.randomevents.RandomEventsMod;
import com.randomevents.commands.EliminationCeremony;
import com.randomevents.manager.RandomEventManager;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.commands.SharedSuggestionProvider;
import net.minecraft.commands.arguments.EntityArgument;
import net.minecraft.network.chat.Component;
import net.minecraft.server.level.ServerPlayer;

public class EventCommands {

    // Event ID suggestions for tab completion
    private static final SuggestionProvider<CommandSourceStack> EVENT_SUGGESTIONS = (context, builder) -> {
        return SharedSuggestionProvider.suggest(new String[]{
            "instant_hunger", "health_swap", "half_heart_challenge", "blindness_blast",
            "slow_motion", "mining_fatigue", "levitation_lift", "lightning_storm",
            "instant_night", "weather_chaos", "lava_rain", "ice_age", "desert_storm",
            "tree_explosion", "sky_islands", "underground_ocean", "cow_chaos",
            "inventory_bomb", "random_teleport", "player_swap",
            "leaf_bomb", "bedrock_hole", "villager_takeover",
            "backwards_controls", "baby_zombie_swarm",
            "hot_potato_bomb", "item_rain", "super_speed",
            "chicken_rain", "dance_party", "chaos_theory",
            "tsunami_wave", "tornado", "xray_vision",
            "everything_explodes", "spotlight", "demolition_day",
            "crouch_tnt", "arrow_storm_ocean",
            "ultimate_event", "potion_soup", "lava_ocean", "giant_food", "anvil_rain",
            "enderman_invasion", "sinking_world", "mob_launch",
            "hot_feet", "creeper_cannon", "color_match_puzzle", "lava_geyser", "diamond_downgrade",
            "nether_invasion", "human_chain", "heavy_snow", "mob_magnets", "diamond_paradise", "magma_age", "creeper_rodeo", "climb_the_ladder", "slot_machine_inventory", "drop_held_item"
        }, builder);
    };

    public static void register(CommandDispatcher<CommandSourceStack> dispatcher) {
        dispatcher.register(Commands.literal("randomevents")
            .requires(source -> source.hasPermission(2)) // Requires OP level 2
            .then(Commands.literal("start")
                .executes(EventCommands::startEvents))
            .then(Commands.literal("stop")
                .executes(EventCommands::stopEvents))
            .then(Commands.literal("reset")
                .executes(EventCommands::resetEvents))
            .then(Commands.literal("status")
                .executes(EventCommands::statusEvents))
            .then(Commands.literal("trigger")
                .then(Commands.argument("event_id", StringArgumentType.string())
                    .suggests(EVENT_SUGGESTIONS)
                    .executes(EventCommands::triggerSpecificEvent)))
            .then(Commands.literal("execute")
                .then(Commands.argument("player", EntityArgument.player())
                    .executes(EventCommands::executePlayer)))
            .then(Commands.literal("eventnum")
                .then(Commands.argument("number", IntegerArgumentType.integer(0, 100))
                    .executes(EventCommands::setEventNumber)))
            .then(Commands.literal("list")
                .executes(EventCommands::listEvents))
            .executes(EventCommands::helpCommand));
    }
    
    private static int startEvents(CommandContext<CommandSourceStack> context) {
        RandomEventManager manager = RandomEventsMod.getEventManager();
        if (manager == null) {
            context.getSource().sendFailure(Component.literal("§cEvent manager not initialized!"));
            return 0;
        }
        
        if (manager.startEvents()) {
            context.getSource().sendSuccess(() -> Component.literal("§aRandom events started!"), true);
            return 1;
        } else {
            context.getSource().sendFailure(Component.literal("§cEvents are already running or no events available!"));
            return 0;
        }
    }
    
    private static int stopEvents(CommandContext<CommandSourceStack> context) {
        RandomEventManager manager = RandomEventsMod.getEventManager();
        if (manager == null) {
            context.getSource().sendFailure(Component.literal("§cEvent manager not initialized!"));
            return 0;
        }
        
        if (manager.stopEvents()) {
            context.getSource().sendSuccess(() -> Component.literal("§cRandom events stopped!"), true);
            return 1;
        } else {
            context.getSource().sendFailure(Component.literal("§cEvents are not currently running!"));
            return 0;
        }
    }
    
    private static int resetEvents(CommandContext<CommandSourceStack> context) {
        RandomEventManager manager = RandomEventsMod.getEventManager();
        if (manager == null) {
            context.getSource().sendFailure(Component.literal("§cEvent manager not initialized!"));
            return 0;
        }
        
        manager.resetEvents();
        context.getSource().sendSuccess(() -> Component.literal("§eEvent pool reset! All events are now available."), true);
        return 1;
    }
    
    private static int statusEvents(CommandContext<CommandSourceStack> context) {
        RandomEventManager manager = RandomEventsMod.getEventManager();
        if (manager == null) {
            context.getSource().sendFailure(Component.literal("§cEvent manager not initialized!"));
            return 0;
        }
        
        boolean active = manager.isGameActive();
        int available = manager.getAvailableEventCount();
        int total = manager.getTotalEventCount();
        int used = manager.getUsedEventCount();
        
        context.getSource().sendSuccess(() -> Component.literal(
            "§6=== Random Events Status ===\n" +
            "§7Active: " + (active ? "§aYes" : "§cNo") + "\n" +
            "§7Available Events: §e" + available + "§7/§e" + total + "\n" +
            "§7Used Events: §e" + used + "§7/§e" + total
        ), false);
        
        return 1;
    }
    
    private static int triggerSpecificEvent(CommandContext<CommandSourceStack> context) {
        RandomEventManager manager = RandomEventsMod.getEventManager();
        if (manager == null) {
            context.getSource().sendFailure(Component.literal("§cEvent manager not initialized!"));
            return 0;
        }

        String eventId = StringArgumentType.getString(context, "event_id");

        if (manager.triggerSpecificEvent(eventId)) {
            context.getSource().sendSuccess(() -> Component.literal("§aTriggered event: " + eventId), true);
            return 1;
        } else {
            context.getSource().sendFailure(Component.literal("§cEvent not found or failed to trigger: " + eventId));
            return 0;
        }
    }

    private static int listEvents(CommandContext<CommandSourceStack> context) {
        context.getSource().sendSuccess(() -> Component.literal(
            "§6=== Available Events (65 Total) - Page 1/5 ===\n" +
            "§einstant_hunger §7- All players lose all hunger\n" +
            "§ehealth_swap §7- Players' health gets redistributed\n" +
            "§ehalf_heart_challenge §7- Players reduced to 1 heart\n" +
            "§eblindness_blast §7- All players blinded\n" +
            "§eslow_motion §7- All players get slowness\n" +
            "§emining_fatigue §7- All players get mining fatigue\n" +
            "§elevitation_lift §7- All players levitate\n" +
            "§elightning_storm §7- Lightning strikes around players\n" +
            "§einstant_night §7- Time set to midnight\n" +
            "§eweather_chaos §7- Weather changes rapidly\n" +
            "§elava_rain §7- Lava blocks fall from sky\n" +
            "§eice_age §7- Water turns to ice\n" +
            "§edesert_storm §7- MASSIVE sand storm\n" +
            "§etree_explosion §7- All tree logs turn into TNT\n" +
            "§esky_islands §7- Floating platforms appear\n" +
            "§eunderground_ocean §7- Area below fills with water"
        ), false);

        // Send second page
        context.getSource().sendSuccess(() -> Component.literal(
            "§6=== Available Events - Page 2/5 ===\n" +
            "§ecow_chaos §7- Aggressive cows chase players\n" +
            "§einventory_bomb §7- Drop all TNT in 10 seconds or explode\n" +
            "§erandom_teleport §7- Players teleport randomly\n" +
            "§eplayer_swap §7- Players swap positions\n" +
            "§eleaf_bomb §7- All leaves turn into TNT\n" +
            "§ebedrock_hole §7- Creates holes to bedrock\n" +
            "§evillager_takeover §7- Villagers follow then explode\n" +
            "§ebackwards_controls §7- Movement controls reversed\n" +
            "§ebaby_zombie_swarm §7- 100 fast baby zombies spawn\n" +
            "§ehot_potato_bomb §7- Inventory items explode randomly\n" +
            "§eitem_rain §7- Random items fall from sky"
        ), false);

        // Send third page
        context.getSource().sendSuccess(() -> Component.literal(
            "§6=== Available Events - Page 3/5 ===\n" +
            "§esuper_speed §7- 5x speed but uncontrollable\n" +
            "§echicken_rain §7- Chickens fall from sky\n" +
            "§edance_party §7- Players dance uncontrollably\n" +
            "§echaos_theory §7- 3 random events at once!\n" +
            "§etsunami_wave §7- Massive water wall sweeps map\n" +
            "§etornado §7- Spinning vortex picks up players\n" +
            "§exray_vision §7- See through blocks for 1 minute\n" +
            "§eeverything_explodes §7- Random harmless explosions\n" +
            "§espotlight §7- One player followed by bright light\n" +
            "§edemolition_day §7- Random structures demolished\n" +
            "§ecrouch_tnt §7- Crouching spawns TNT in front of you\n" +
            "§earrow_storm_ocean §7- Deep water + massive arrow storm"
        ), false);

        // Send fourth page
        context.getSource().sendSuccess(() -> Component.literal(
            "§6=== Available Events - Page 4/5 ===\n" +
            "§eultimate_event §7- 5 random events simultaneously!\n" +
            "§epotion_soup §7- Random potion effects rain from sky\n" +
            "§elava_ocean §7- All water becomes lava for 2 minutes\n" +
            "§egiant_food §7- Massive food items fall from sky\n" +
            "§eanvil_rain §7- Massive amounts of anvils fall from sky"
        ), false);

        // Send fifth page
        context.getSource().sendSuccess(() -> Component.literal(
            "§6=== Available Events - Page 5/5 ===\n" +
            "§eenderman_invasion §7- 50+ endermen steal blocks and teleport players\n" +
            "§esinking_world §7- Ground level drops by 10 blocks then restores\n" +
            "§emob_launch §7- All nearby mobs fly up and drop valuable loot\n" +
            "§ehot_feet §7- Standing still turns ground under you into lava\n" +
            "§ecreeper_cannon §7- Creepers launched at high speed towards players\n" +
            "§ecolor_match_puzzle §7- Work together to recreate color pattern or explode\n" +
            "§elava_geyser §7- Lava shoots up from the ground in towering geysers\n" +
            "§ediamond_downgrade §7- All diamond armor becomes gold armor (conditional)\n" +
            "§enether_invasion §7- Nether portals spawn - enter within 15 seconds or die!\n" +
            "§ehuman_chain §7- Players must stay within 5 blocks of each other or take damage\n" +
            "§eheavy_snow §7- A LOT of snow falls from sky, reducing visibility\n" +
            "§emob_magnets §7- Safe animals are attracted to each player\n" +
            "§ediamond_paradise §7- Full diamond set spawns but runs away when you try to pick it up\n" +
            "§emagma_age §7- Massive magma age covers 500x500 area - ground turns to magma\n" +
            "§ecreeper_rodeo §7- You're forced to ride a creeper for 10 seconds. If it explodes, you lose!\n" +
            "§eclimb_the_ladder §7- Tall ladder structures with treasure chests spawn - climb for loot!\n" +
            "§eslot_machine_inventory §7- 3 random items scroll like a slot machine and you get one\n" +
            "§edrop_held_item §7- Force the player to drop the item they're holding. It flies 10 blocks away."
        ), false);

        return 1;
    }

    private static int executePlayer(CommandContext<CommandSourceStack> context) {
        try {
            ServerPlayer targetPlayer = EntityArgument.getPlayer(context, "player");

            // Create and execute the elimination ceremony
            EliminationCeremony.execute(targetPlayer);

            context.getSource().sendSuccess(() -> Component.literal("§c§lExecuting player: " + targetPlayer.getName().getString()), false);
            return 1;
        } catch (Exception e) {
            context.getSource().sendFailure(Component.literal("§cFailed to execute player: " + e.getMessage()));
            return 0;
        }
    }

    private static int setEventNumber(CommandContext<CommandSourceStack> context) {
        try {
            int eventNumber = IntegerArgumentType.getInteger(context, "number");
            RandomEventManager manager = RandomEventsMod.getEventManager();

            if (manager == null) {
                context.getSource().sendFailure(Component.literal("§cEvent manager not initialized!"));
                return 0;
            }

            manager.setCurrentEventNumber(eventNumber);

            context.getSource().sendSuccess(() -> Component.literal("§a§lEvent number set to: §e" + eventNumber + "§a/100"), true);
            return 1;
        } catch (Exception e) {
            context.getSource().sendFailure(Component.literal("§cFailed to set event number: " + e.getMessage()));
            return 0;
        }
    }

    private static int helpCommand(CommandContext<CommandSourceStack> context) {
        context.getSource().sendSuccess(() -> Component.literal(
            "§6=== Random Events Commands ===\n" +
            "§e/randomevents start §7- Start the random events\n" +
            "§e/randomevents stop §7- Stop the random events\n" +
            "§e/randomevents reset §7- Reset the event pool\n" +
            "§e/randomevents status §7- Show current status\n" +
            "§e/randomevents trigger <event_id> §7- Manually trigger an event\n" +
            "§e/randomevents execute <player> §7- Execute elimination ceremony\n" +
            "§e/randomevents eventnum <number> §7- Set current event number (0-100)\n" +
            "§e/randomevents list §7- List all available events"
        ), false);

        return 1;
    }
}
