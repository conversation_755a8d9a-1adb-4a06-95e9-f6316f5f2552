package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.animal.*;
import net.minecraft.world.entity.animal.horse.Horse;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class MobMagnetsEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    private static final List<Animal> spawnedAnimals = new ArrayList<>();
    
    @Override
    public String getId() {
        return "mob_magnets";
    }
    
    @Override
    public String getName() {
        return "§6§lMob Magnets!";
    }
    
    @Override
    public String getDescription() {
        return "Safe animals are attracted to each player for 60 seconds";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        spawnedAnimals.clear();
        
        // Show single message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§7§oSomething weird is going on...")));
        }
        
        // Wait 2 seconds then start the mob magnet effect
        scheduler.schedule(() -> {
            // Check each player and spawn animals if needed
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                ServerLevel level = player.serverLevel();
                BlockPos playerPos = player.blockPosition();
                
                // Count nearby safe animals within 15 blocks
                List<Animal> nearbyAnimals = level.getEntitiesOfClass(Animal.class, 
                    new net.minecraft.world.phys.AABB(playerPos).inflate(15.0));
                
                // Filter to only safe animals (no wolves, etc.)
                int safeAnimalCount = 0;
                for (Animal animal : nearbyAnimals) {
                    if (isSafeAnimal(animal)) {
                        safeAnimalCount++;
                    }
                }
                
                // If player has less than 3 safe animals nearby, spawn some
                if (safeAnimalCount < 3) {
                    int animalsToSpawn = 3 + random.nextInt(3); // 3-5 animals
                    spawnAnimalsAroundPlayer(level, player, animalsToSpawn);
                }
            }
            
            // Start the magnet effect for 60 seconds (check every 2 seconds)
            for (int i = 0; i < 30; i++) { // 30 checks over 60 seconds
                scheduler.schedule(() -> {
                    applyMagnetEffect(server);
                }, i * 2L, TimeUnit.SECONDS);
            }
            
        }, 2L, TimeUnit.SECONDS);
    }
    
    private boolean isSafeAnimal(Animal animal) {
        return animal instanceof Pig || 
               animal instanceof Cow || 
               animal instanceof Chicken || 
               animal instanceof Sheep || 
               animal instanceof Horse ||
               animal instanceof Rabbit;
    }
    
    private void spawnAnimalsAroundPlayer(ServerLevel level, ServerPlayer player, int count) {
        BlockPos playerPos = player.blockPosition();
        
        for (int i = 0; i < count; i++) {
            // Random position 8-20 blocks away from player
            int distance = 8 + random.nextInt(13); // 8-20 blocks
            double angle = random.nextDouble() * 2 * Math.PI;
            int x = playerPos.getX() + (int)(Math.cos(angle) * distance);
            int z = playerPos.getZ() + (int)(Math.sin(angle) * distance);
            int y = level.getHeight(net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING, x, z);
            
            BlockPos spawnPos = new BlockPos(x, y, z);
            
            // Make sure spawn position is safe
            if (level.getBlockState(spawnPos).isAir() && 
                level.getBlockState(spawnPos.above()).isAir() &&
                !level.getBlockState(spawnPos.below()).isAir()) {
                
                // Randomly choose animal type
                Animal animal = createRandomSafeAnimal(level);
                if (animal != null) {
                    animal.moveTo(spawnPos.getX() + 0.5, spawnPos.getY(), spawnPos.getZ() + 0.5);
                    level.addFreshEntity(animal);
                    spawnedAnimals.add(animal);
                }
            }
        }
    }
    
    private Animal createRandomSafeAnimal(ServerLevel level) {
        int animalType = random.nextInt(6);
        try {
            switch (animalType) {
                case 0:
                    return new Pig(EntityType.PIG, level);
                case 1:
                    return new Cow(EntityType.COW, level);
                case 2:
                    return new Chicken(EntityType.CHICKEN, level);
                case 3:
                    return new Sheep(EntityType.SHEEP, level);
                case 4:
                    return new Horse(EntityType.HORSE, level);
                case 5:
                    return new Rabbit(EntityType.RABBIT, level);
                default:
                    return new Pig(EntityType.PIG, level);
            }
        } catch (Exception e) {
            return null;
        }
    }
    
    private void applyMagnetEffect(MinecraftServer server) {
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            ServerLevel level = player.serverLevel();
            BlockPos playerPos = player.blockPosition();
            
            // Find all safe animals within 30 blocks
            List<Animal> nearbyAnimals = level.getEntitiesOfClass(Animal.class, 
                new net.minecraft.world.phys.AABB(playerPos).inflate(30.0));
            
            for (Animal animal : nearbyAnimals) {
                if (isSafeAnimal(animal) && animal.isAlive()) {
                    // Calculate direction from animal to player
                    double dx = player.getX() - animal.getX();
                    double dz = player.getZ() - animal.getZ();
                    double distance = Math.sqrt(dx * dx + dz * dz);
                    
                    // Only attract if more than 2 blocks away (don't make them stack on player)
                    if (distance > 2.0 && distance < 30.0) {
                        // Normalize direction and apply attraction force
                        double force = 0.15; // Gentle attraction
                        dx = (dx / distance) * force;
                        dz = (dz / distance) * force;
                        
                        // Apply movement toward player
                        animal.setDeltaMovement(
                            animal.getDeltaMovement().x + dx,
                            animal.getDeltaMovement().y,
                            animal.getDeltaMovement().z + dz
                        );
                    }
                }
            }
        }
    }
}
