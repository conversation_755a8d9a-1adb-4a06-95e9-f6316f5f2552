package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.level.block.Blocks;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class CraftAwayEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final List<BlockPos> spawnedCraftingTables = new ArrayList<>();
    private static final Map<ServerPlayer, Boolean> lockedPlayers = new HashMap<>();
    
    @Override
    public String getId() {
        return "craft_away";
    }
    
    @Override
    public String getName() {
        return "§e§lCraft Away!";
    }
    
    @Override
    public String getDescription() {
        return "Place a crafting table nearby that opens a fake UI and locks you in it for 10 seconds";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        spawnedCraftingTables.clear();
        lockedPlayers.clear();
        
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            ServerLevel level = player.serverLevel();
            BlockPos playerPos = player.blockPosition();
            
            // Place crafting table 3 blocks in front of player
            BlockPos craftingTablePos = playerPos.offset(3, 0, 0);
            
            // Make sure the position is valid
            if (level.isInWorldBounds(craftingTablePos)) {
                level.setBlock(craftingTablePos, Blocks.CRAFTING_TABLE.defaultBlockState(), 3);
                spawnedCraftingTables.add(craftingTablePos);
                
                // Show message
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§e§lA crafting table appeared! §7Right-click it to craft...")));
            }
        }
        
        // Start monitoring for players interacting with crafting tables
        monitorCraftingTableInteractions(server);
        
        // Remove crafting tables after 60 seconds
        scheduler.schedule(() -> {
            cleanupCraftingTables(server);
        }, 60L, TimeUnit.SECONDS);
    }
    
    private void monitorCraftingTableInteractions(MinecraftServer server) {
        // Check every 0.5 seconds for 60 seconds
        for (int i = 0; i < 120; i++) { // 120 checks over 60 seconds
            scheduler.schedule(() -> {
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    // Check if player is near a crafting table and not already locked
                    if (!lockedPlayers.getOrDefault(player, false)) {
                        BlockPos playerPos = player.blockPosition();
                        
                        for (BlockPos craftingTablePos : spawnedCraftingTables) {
                            double distance = playerPos.distSqr(craftingTablePos);
                            
                            // If player is within 2 blocks of a crafting table
                            if (distance <= 4.0) {
                                lockPlayerInCraftingUI(player);
                                break;
                            }
                        }
                    }
                }
            }, i * 500L, TimeUnit.MILLISECONDS);
        }
    }
    
    private void lockPlayerInCraftingUI(ServerPlayer player) {
        lockedPlayers.put(player, true);
        
        // Show fake crafting UI message
        player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
            net.minecraft.network.chat.Component.literal("§c§lYou're trapped in the crafting UI for 10 seconds!")));
        
        // Freeze the player
        freezePlayer(player);
        
        // Release player after 10 seconds
        scheduler.schedule(() -> {
            unfreezePlayer(player);
            lockedPlayers.put(player, false);
            
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§a§lYou escaped the crafting table trap!")));
        }, 10L, TimeUnit.SECONDS);
    }
    
    private void freezePlayer(ServerPlayer player) {
        // Store original position
        player.getPersistentData().putDouble("craft_away_x", player.getX());
        player.getPersistentData().putDouble("craft_away_y", player.getY());
        player.getPersistentData().putDouble("craft_away_z", player.getZ());
        player.getPersistentData().putBoolean("craft_away_frozen", true);
    }
    
    private void unfreezePlayer(ServerPlayer player) {
        player.getPersistentData().putBoolean("craft_away_frozen", false);
    }
    
    private void cleanupCraftingTables(MinecraftServer server) {
        for (ServerLevel level : server.getAllLevels()) {
            for (BlockPos pos : spawnedCraftingTables) {
                if (level.isInWorldBounds(pos)) {
                    level.setBlock(pos, Blocks.AIR.defaultBlockState(), 3);
                }
            }
        }
        
        spawnedCraftingTables.clear();
        
        // Unfreeze any remaining locked players
        for (ServerPlayer player : lockedPlayers.keySet()) {
            unfreezePlayer(player);
        }
        lockedPlayers.clear();
    }
}
