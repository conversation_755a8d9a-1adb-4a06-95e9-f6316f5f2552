package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.item.PrimedTnt;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class CraftAwayEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final List<BlockPos> spawnedCraftingTables = new ArrayList<>();
    private static final Map<ServerPlayer, Boolean> lockedPlayers = new HashMap<>();
    private static final Set<ServerPlayer> frozenPlayers = new HashSet<>();
    private static boolean eventActive = false;
    
    @Override
    public String getId() {
        return "craft_away";
    }
    
    @Override
    public String getName() {
        return "§e§lCraft Away!";
    }
    
    @Override
    public String getDescription() {
        return "Place a crafting table nearby that opens a fake UI and locks you in it for 10 seconds";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        spawnedCraftingTables.clear();
        lockedPlayers.clear();
        frozenPlayers.clear();
        eventActive = true;

        // Register movement prevention event handler
        MinecraftForge.EVENT_BUS.register(MovementPrevention.class);
        
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            ServerLevel level = player.serverLevel();
            BlockPos playerPos = player.blockPosition();
            
            // Place crafting table 3 blocks in front of player
            BlockPos craftingTablePos = playerPos.offset(3, 0, 0);
            
            // Make sure the position is valid
            if (level.isInWorldBounds(craftingTablePos)) {
                level.setBlock(craftingTablePos, Blocks.CRAFTING_TABLE.defaultBlockState(), 3);
                spawnedCraftingTables.add(craftingTablePos);
                
                // Show message for only 3 seconds
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§e§lA wild crafting table has appeared!")));

                // Clear message after 3 seconds
                scheduler.schedule(() -> {
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                        net.minecraft.network.chat.Component.literal("")));
                }, 3L, TimeUnit.SECONDS);
            }
        }
        
        // Start monitoring for players interacting with crafting tables
        monitorCraftingTableInteractions(server);
        
        // Remove crafting tables after 60 seconds
        scheduler.schedule(() -> {
            cleanupCraftingTables(server);
        }, 60L, TimeUnit.SECONDS);
    }
    
    private void monitorCraftingTableInteractions(MinecraftServer server) {
        // Check every 0.5 seconds for 60 seconds
        for (int i = 0; i < 120; i++) { // 120 checks over 60 seconds
            scheduler.schedule(() -> {
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    // Check if player is near a crafting table and not already locked
                    if (!lockedPlayers.getOrDefault(player, false)) {
                        BlockPos playerPos = player.blockPosition();
                        
                        for (BlockPos craftingTablePos : spawnedCraftingTables) {
                            double distance = playerPos.distSqr(craftingTablePos);
                            
                            // If player is within 2 blocks of a crafting table
                            if (distance <= 4.0) {
                                lockPlayerInCraftingUI(player);
                                break;
                            }
                        }
                    }
                }
            }, i * 500L, TimeUnit.MILLISECONDS);
        }
    }
    
    private void lockPlayerInCraftingUI(ServerPlayer player) {
        lockedPlayers.put(player, true);

        // Show "That was a mistake..." message
        player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
            net.minecraft.network.chat.Component.literal("§c§lThat was a mistake...")));

        // Spawn harmless TNT above the player
        spawnHarmlessTNT(player);

        // Freeze the player properly
        freezePlayer(player);

        // Release player after 15 seconds (no escape message)
        scheduler.schedule(() -> {
            unfreezePlayer(player);
            lockedPlayers.put(player, false);
        }, 15L, TimeUnit.SECONDS);
    }

    private void spawnHarmlessTNT(ServerPlayer player) {
        ServerLevel level = player.serverLevel();
        BlockPos playerPos = player.blockPosition();

        // Spawn 5-8 primed TNT entities above the player
        int tntCount = 5 + level.getRandom().nextInt(4); // 5-8 TNT

        for (int i = 0; i < tntCount; i++) {
            PrimedTnt tnt = EntityType.TNT.create(level);
            if (tnt != null) {
                // Position TNT above and around the player
                double offsetX = (level.getRandom().nextDouble() - 0.5) * 4; // -2 to +2 blocks
                double offsetZ = (level.getRandom().nextDouble() - 0.5) * 4; // -2 to +2 blocks
                double offsetY = 3 + level.getRandom().nextDouble() * 3; // 3-6 blocks above

                tnt.moveTo(playerPos.getX() + offsetX, playerPos.getY() + offsetY, playerPos.getZ() + offsetZ);
                tnt.setFuse(200); // 10 seconds fuse (200 ticks)

                level.addFreshEntity(tnt);

                // Remove TNT after 10 seconds before it can explode
                scheduler.schedule(() -> {
                    if (tnt.isAlive()) {
                        tnt.remove(net.minecraft.world.entity.Entity.RemovalReason.DISCARDED);
                    }
                }, 10L, TimeUnit.SECONDS);
            }
        }
    }
    
    private void freezePlayer(ServerPlayer player) {
        frozenPlayers.add(player);
    }

    private void unfreezePlayer(ServerPlayer player) {
        frozenPlayers.remove(player);
    }
    
    private void cleanupCraftingTables(MinecraftServer server) {
        for (ServerLevel level : server.getAllLevels()) {
            for (BlockPos pos : spawnedCraftingTables) {
                if (level.isInWorldBounds(pos)) {
                    level.setBlock(pos, Blocks.AIR.defaultBlockState(), 3);
                }
            }
        }
        
        spawnedCraftingTables.clear();

        // Unfreeze any remaining locked players
        for (ServerPlayer player : lockedPlayers.keySet()) {
            unfreezePlayer(player);
        }
        lockedPlayers.clear();
        frozenPlayers.clear();
        eventActive = false;
        MovementPrevention.clearFrozenPositions();

        // Unregister movement prevention event handler
        try {
            MinecraftForge.EVENT_BUS.unregister(MovementPrevention.class);
        } catch (Exception e) {
            // Ignore if already unregistered
        }
    }

    // Event handler class for movement prevention
    public static class MovementPrevention {
        private static final Map<ServerPlayer, Vec3> frozenPositions = new HashMap<>();

        @SubscribeEvent
        public static void onPlayerTick(TickEvent.PlayerTickEvent event) {
            if (!eventActive || event.phase != TickEvent.Phase.START) return;

            if (event.player instanceof ServerPlayer serverPlayer) {
                if (frozenPlayers.contains(serverPlayer)) {
                    // Store the frozen position when first frozen
                    if (!frozenPositions.containsKey(serverPlayer)) {
                        frozenPositions.put(serverPlayer, serverPlayer.position());
                    }

                    Vec3 frozenPos = frozenPositions.get(serverPlayer);
                    Vec3 currentPos = serverPlayer.position();

                    // Check if player has moved from frozen position
                    double distance = frozenPos.distanceTo(currentPos);
                    if (distance > 0.1) { // Allow tiny movements for stability
                        // Teleport back to frozen position
                        serverPlayer.teleportTo(frozenPos.x, frozenPos.y, frozenPos.z);
                    }

                    // AGGRESSIVE movement prevention - completely lock all movement
                    Vec3 currentVelocity = serverPlayer.getDeltaMovement();

                    // Cancel ALL movement including horizontal and vertical
                    if (Math.abs(currentVelocity.x) > 0.001 || Math.abs(currentVelocity.z) > 0.001 || Math.abs(currentVelocity.y) > 0.001) {
                        serverPlayer.setDeltaMovement(0, 0, 0);
                        serverPlayer.hurtMarked = true; // Force update
                    }

                    // Additional prevention: Reset impulse and movement flags
                    if (serverPlayer.hasImpulse) {
                        serverPlayer.hasImpulse = false;
                    }
                }
            }
        }

        public static void clearFrozenPositions() {
            frozenPositions.clear();
        }
    }
}
