package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.projectile.ThrownPotion;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.item.alchemy.PotionUtils;
import net.minecraft.world.item.alchemy.Potions;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

public class PotionSoupEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    
    @Override
    public String getId() {
        return "potion_soup";
    }
    
    @Override
    public String getName() {
        return "§5§lPotion Soup!";
    }
    
    @Override
    public String getDescription() {
        return "GOOD splash potions rain from sky for 30 seconds - beneficial chaos!";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        // Show warning message for 5 seconds only
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§5§lPotions are raining from the sky!")));
        }

        // Clear message after 5 seconds
        scheduler.schedule(() -> {
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("")));
            }
        }, 5L, TimeUnit.SECONDS);

        // Drop MASSIVE amounts of splash potions every 1 second for 30 seconds
        for (int i = 0; i < 30; i++) { // 30 seconds / 1 second = 30 drops
            scheduler.schedule(() -> {
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    ServerLevel level = player.serverLevel();
                    BlockPos playerPos = player.blockPosition();

                    // Drop 15-25 good splash potions around each player (MASSIVE increase!)
                    int potionCount = 15 + ThreadLocalRandom.current().nextInt(11);

                    for (int j = 0; j < potionCount; j++) {
                        // Random position around player (larger area)
                        int x = playerPos.getX() + ThreadLocalRandom.current().nextInt(50) - 25;
                        int z = playerPos.getZ() + ThreadLocalRandom.current().nextInt(50) - 25;
                        int y = playerPos.getY() + 20 + ThreadLocalRandom.current().nextInt(15);

                        // Create good splash potion
                        ItemStack splashPotion = createGoodSplashPotion();

                        // Create thrown potion entity
                        ThrownPotion thrownPotion = new ThrownPotion(level, x, y, z);
                        thrownPotion.setItem(splashPotion);
                        thrownPotion.setDeltaMovement(0, -0.8, 0); // Fall faster

                        level.addFreshEntity(thrownPotion);
                    }
                }
            }, i * 1L, TimeUnit.SECONDS); // Every 1 second
        }
    }
    
    private ItemStack createGoodSplashPotion() {
        // Array of GOOD potion types only (NO INVISIBILITY)
        net.minecraft.world.item.alchemy.Potion[] goodPotions = {
            Potions.HEALING,
            Potions.STRONG_HEALING,
            Potions.REGENERATION,
            Potions.LONG_REGENERATION,
            Potions.STRONG_REGENERATION,
            Potions.SWIFTNESS,
            Potions.LONG_SWIFTNESS,
            Potions.STRONG_SWIFTNESS,
            Potions.STRENGTH,
            Potions.LONG_STRENGTH,
            Potions.STRONG_STRENGTH,
            Potions.LEAPING,
            Potions.LONG_LEAPING,
            Potions.STRONG_LEAPING,
            Potions.FIRE_RESISTANCE,
            Potions.LONG_FIRE_RESISTANCE,
            Potions.WATER_BREATHING,
            Potions.LONG_WATER_BREATHING,
            Potions.NIGHT_VISION,
            Potions.LONG_NIGHT_VISION
            // Removed invisibility potions as requested
        };

        // Pick random good potion
        net.minecraft.world.item.alchemy.Potion randomPotion = goodPotions[ThreadLocalRandom.current().nextInt(goodPotions.length)];

        // Create splash potion item
        ItemStack splashPotion = new ItemStack(Items.SPLASH_POTION);
        return PotionUtils.setPotion(splashPotion, randomPotion);
    }
}
