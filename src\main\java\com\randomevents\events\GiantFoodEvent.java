package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.item.ItemEntity;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.level.block.Blocks;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

public class GiantFoodEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    
    @Override
    public String getId() {
        return "giant_food";
    }
    
    @Override
    public String getName() {
        return "§6§lGiant Food Rain!";
    }
    
    @Override
    public String getDescription() {
        return "Massive food items fall from sky - eat them to survive hunger!";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§6§lGiant food is falling from the sky! §7Collect it before you starve!")));
        }
        
        // Make all players very hungry first
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.getFoodData().setFoodLevel(1); // Almost starving
        }
        
        // Drop giant food items every 5 seconds for 30 seconds
        for (int i = 0; i < 6; i++) { // 30 seconds / 5 seconds = 6 drops
            scheduler.schedule(() -> {
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    ServerLevel level = player.serverLevel();
                    BlockPos playerPos = player.blockPosition();
                    
                    // Drop 3-5 giant food items around each player
                    int foodCount = 3 + ThreadLocalRandom.current().nextInt(3);
                    
                    for (int j = 0; j < foodCount; j++) {
                        // Random position around player
                        int x = playerPos.getX() + ThreadLocalRandom.current().nextInt(40) - 20;
                        int z = playerPos.getZ() + ThreadLocalRandom.current().nextInt(40) - 20;
                        int y = playerPos.getY() + 20 + ThreadLocalRandom.current().nextInt(10); // High in the sky
                        
                        BlockPos dropPos = new BlockPos(x, y, z);
                        
                        // Create giant food item (stack of 64)
                        ItemStack giantFood = getRandomGiantFood();
                        
                        // Create item entity
                        ItemEntity foodEntity = new ItemEntity(level, dropPos.getX(), dropPos.getY(), dropPos.getZ(), giantFood);
                        
                        // Make it fall faster and glow
                        foodEntity.setDeltaMovement(0, -0.5, 0);
                        foodEntity.setGlowingTag(true);
                        
                        level.addFreshEntity(foodEntity);
                    }
                }
                
                // Show periodic messages
                String[] foodMessages = {
                    "§6§lGiant food raining down!",
                    "§e§lMassive meals from the sky!",
                    "§6§lHuge helpings falling!",
                    "§e§lSuper-sized sustenance!",
                    "§6§lEnormous edibles everywhere!"
                };
                
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    if (ThreadLocalRandom.current().nextInt(3) == 0) { // 33% chance
                        String message = foodMessages[ThreadLocalRandom.current().nextInt(foodMessages.length)];
                        player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                            net.minecraft.network.chat.Component.literal(message)));
                    }
                }
                
            }, i * 5L, TimeUnit.SECONDS);
        }
        
        // Final message
        scheduler.schedule(() -> {
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§6§lThe giant food rain has ended! §7Hope you're full!")));
            }
        }, 35L, TimeUnit.SECONDS); // 5 seconds after the last drop
    }
    
    private ItemStack getRandomGiantFood() {
        // Array of food items that will be "giant" (stack of 64)
        ItemStack[] foods = {
            new ItemStack(Items.BREAD, 64),
            new ItemStack(Items.COOKED_BEEF, 64),
            new ItemStack(Items.COOKED_PORKCHOP, 64),
            new ItemStack(Items.COOKED_CHICKEN, 64),
            new ItemStack(Items.COOKED_MUTTON, 64),
            new ItemStack(Items.COOKED_SALMON, 64),
            new ItemStack(Items.COOKED_COD, 64),
            new ItemStack(Items.BAKED_POTATO, 64),
            new ItemStack(Items.CARROT, 64),
            new ItemStack(Items.APPLE, 64),
            new ItemStack(Items.GOLDEN_APPLE, 32),
            new ItemStack(Items.MELON_SLICE, 64),
            new ItemStack(Items.SWEET_BERRIES, 64),
            new ItemStack(Items.CAKE, 16),
            new ItemStack(Items.PUMPKIN_PIE, 64),
            new ItemStack(Items.COOKIE, 64),
            new ItemStack(Items.MUSHROOM_STEW, 32),
            new ItemStack(Items.RABBIT_STEW, 32),
            new ItemStack(Items.BEETROOT_SOUP, 32)
        };
        
        return foods[ThreadLocalRandom.current().nextInt(foods.length)];
    }
}
