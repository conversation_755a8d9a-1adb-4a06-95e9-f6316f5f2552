package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.phys.Vec3;

import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.entity.living.LivingEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;

import java.util.concurrent.TimeUnit;

public class TsunamiWaveEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();

    private final List<BlockPos> placedWaterBlocks = new ArrayList<>();
    private static boolean tsunamiActive = false;
    private final List<ScheduledFuture<?>> scheduledTasks = new ArrayList<>();
    
    @Override
    public String getId() {
        return "tsunami_wave";
    }
    
    @Override
    public String getName() {
        return "§1§lTsunami Wave!";
    }
    
    @Override
    public String getDescription() {
        return "Realistic tsunami rises then crashes with massive force, pushing players";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        placedWaterBlocks.clear();
        scheduledTasks.clear();
        tsunamiActive = true;

        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§1§lThe ocean is rising... §7TSUNAMI INCOMING!")));
        }

        // Register event listener for water pushing
        MinecraftForge.EVENT_BUS.register(TsunamiEventHandler.class);

        // GUARANTEED spiraling physics using scheduled tasks (reduced frequency to prevent server overload)
        // Limit to exactly 30 seconds
        for (int i = 0; i < 60; i++) { // 30 seconds * 2 checks per second = 60 checks
            ScheduledFuture<?> task = scheduler.schedule(() -> {
                if (tsunamiActive) {
                    for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                        ServerLevel level = player.serverLevel();
                        BlockPos playerPos = player.blockPosition();

                        // Check if player is in water (any water contact)
                        boolean inWater = level.getBlockState(playerPos).is(Blocks.WATER) ||
                                         level.getBlockState(playerPos.above()).is(Blocks.WATER) ||
                                         level.getBlockState(playerPos.below()).is(Blocks.WATER) ||
                                         level.getBlockState(playerPos.north()).is(Blocks.WATER) ||
                                         level.getBlockState(playerPos.south()).is(Blocks.WATER) ||
                                         level.getBlockState(playerPos.east()).is(Blocks.WATER) ||
                                         level.getBlockState(playerPos.west()).is(Blocks.WATER);

                        if (inWater) {
                            // TSUNAMI SPIRALING PHYSICS - UP/DOWN SPIRALING + FORWARD/BACKWARD PUSHING

                            // 1. VERTICAL SPIRALING motion - up and down in the water
                            double time = System.currentTimeMillis() * 0.03; // Fast time progression
                            double verticalSpiral = Math.sin(time) * 6.0; // Strong up/down spiraling

                            // 2. FORWARD/BACKWARD PUSHING forces
                            double pushTime = time * 0.5; // Slower push rhythm
                            double forwardPush = Math.cos(pushTime) * 8.0; // Strong forward/backward push

                            // 3. HORIZONTAL SPIRALING (left/right)
                            double horizontalSpiral = Math.cos(time * 1.5) * 4.0; // Side-to-side spiraling

                            // 4. Determine push direction based on player facing
                            Vec3 lookDirection = player.getLookAngle();
                            double pushX = lookDirection.x * forwardPush + horizontalSpiral;
                            double pushZ = lookDirection.z * forwardPush;

                            // 5. APPLY TSUNAMI FORCES - vertical spiraling + directional pushing
                            player.setDeltaMovement(pushX, verticalSpiral, pushZ);

                            // 6. MODERATE view spinning for disorientation (not extreme)
                            player.setYRot(player.getYRot() + 30); // Moderate rotation
                            player.setXRot(player.getXRot() + (float)(Math.random() - 0.5) * 60); // Moderate pitch changes

                            // 7. Show tsunami spiral messages
                            if (Math.random() < 0.1) { // 10% chance per tick
                                String[] tsunamiMessages = {
                                    "§1§lYou're spiraling UP and DOWN in the tsunami!",
                                    "§9§lThe wave is pushing you around!",
                                    "§1§lSpiraling through the massive wave!",
                                    "§9§lTsunami forces pushing you forward and back!"
                                };
                                String message = tsunamiMessages[(int)(Math.random() * tsunamiMessages.length)];
                                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                                    net.minecraft.network.chat.Component.literal(message)));
                            }
                        }
                    }
                }
            }, i * 500L, TimeUnit.MILLISECONDS); // Every 500ms (0.5 seconds)
            scheduledTasks.add(task);
        }

        // Phase 1: Create MASSIVE HIGH WALLS that flow VERY FAST toward players (6 seconds)
        for (int i = 0; i < 12; i++) { // 12 waves over 6 seconds = every 0.5 seconds
            final int waveLevel = i;
            ScheduledFuture<?> waveTask = scheduler.schedule(() -> {
                if (!tsunamiActive) return; // Stop if tsunami ended
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    ServerLevel level = player.serverLevel();
                    BlockPos playerPos = player.blockPosition();

                    // Create tsunami wave that starts FAR and moves VERY FAST toward player
                    int waveDistance = 60 - (waveLevel * 5); // Wave starts 60 blocks away and moves 5 blocks closer every 0.5 seconds
                    int waveHeight = 30; // MASSIVE 30 block high walls

                    // Create the approaching wave wall from all 4 directions
                    for (int direction = 0; direction < 4; direction++) {
                        int startX = playerPos.getX();
                        int startZ = playerPos.getZ();

                        // Calculate wave position based on direction
                        switch (direction) {
                            case 0: startX -= waveDistance; break; // From west
                            case 1: startX += waveDistance; break; // From east
                            case 2: startZ -= waveDistance; break; // From north
                            case 3: startZ += waveDistance; break; // From south
                        }

                        // Create ENORMOUS wave wall - 30 blocks high!
                        for (int offset = -80; offset <= 80; offset++) { // Very wide wave
                            for (int height = 0; height <= waveHeight; height++) {
                                BlockPos wavePos;
                                if (direction < 2) { // East/West waves
                                    wavePos = new BlockPos(startX, playerPos.getY() + height, playerPos.getZ() + offset);
                                } else { // North/South waves
                                    wavePos = new BlockPos(playerPos.getX() + offset, playerPos.getY() + height, startZ);
                                }

                                if (level.isInWorldBounds(wavePos) && level.getBlockState(wavePos).isAir()) {
                                    // Use flag 2 to prevent block updates and reduce tick load
                                    level.setBlock(wavePos, Blocks.WATER.defaultBlockState(), 2);
                                    placedWaterBlocks.add(wavePos);
                                }
                            }
                        }
                    }
                }

                // Show tsunami approach messages
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    int distanceLeft = 60 - (waveLevel * 5);
                    if (distanceLeft > 0) {
                        player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                            net.minecraft.network.chat.Component.literal("§1§lTsunami Level " + (waveLevel + 1) + "/12 §7- 30-block HIGH wave " + distanceLeft + " blocks away!")));
                    } else {
                        player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                            net.minecraft.network.chat.Component.literal("§1§l§kTSUNAMI IMPACT!§r §4§lMASSIVE 30-block wave hits!")));
                    }
                }
            }, i * 500L, TimeUnit.MILLISECONDS); // Every 0.5 seconds for FAST approach
            scheduledTasks.add(waveTask);
        }

        // Phase 2: MASSIVE wave crashes FAST (12 seconds)
        ScheduledFuture<?> phase2Task = scheduler.schedule(() -> {
            if (!tsunamiActive) return; // Stop if tsunami ended
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§1§l§kTSUNAMI WAVE INCOMING!§r §4§lBRACE FOR IMPACT!")));
            }

            // Create the MASSIVE wave that moves FAST across the map
            for (int wave = 0; wave < 12; wave++) {
                final int waveNumber = wave;

                ScheduledFuture<?> massiveWaveTask = scheduler.schedule(() -> {
                    if (!tsunamiActive) return; // Stop if tsunami ended
                    for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                        ServerLevel level = player.serverLevel();
                        BlockPos playerPos = player.blockPosition();

                        // Create a MASSIVE moving wall of water that moves FAST
                        int waveOffset = waveNumber * 8; // Wave moves 8 blocks per second (FASTER)

                        for (int x = -50; x <= 50; x++) { // WIDER wave
                            for (int z = -15 + waveOffset; z <= 15 + waveOffset; z++) { // THICKER wave
                                for (int y = 0; y <= 25; y++) { // 25 blocks high MASSIVE wave (HIGHER)
                                    BlockPos wavePos = playerPos.offset(x, y, z);

                                    if (level.isInWorldBounds(wavePos) &&
                                        level.getBlockState(wavePos).isAir()) {
                                        // Use flag 2 to prevent block updates and reduce tick load
                                        level.setBlock(wavePos, Blocks.WATER.defaultBlockState(), 2);
                                        placedWaterBlocks.add(wavePos);
                                    }
                                }
                            }
                        }
                    }
                }, wave * 1L, TimeUnit.SECONDS);
                scheduledTasks.add(massiveWaveTask);
            }
        }, 8L, TimeUnit.SECONDS); // Start sooner
        scheduledTasks.add(phase2Task);

        // Stop tsunami effect after 30 seconds (much shorter)
        ScheduledFuture<?> stopTask = scheduler.schedule(() -> {
            tsunamiActive = false;

            // Unregister event handler
            try {
                MinecraftForge.EVENT_BUS.unregister(TsunamiEventHandler.class);
            } catch (Exception e) {
                // Ignore unregister errors
            }

            // Cancel all remaining scheduled tasks immediately
            for (ScheduledFuture<?> task : scheduledTasks) {
                if (!task.isDone()) {
                    task.cancel(true); // Use true to interrupt if running
                }
            }
            scheduledTasks.clear();

            // Show end message
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§7§lTsunami is receding... §aThe chaos is ending!")));
            }
        }, 30L, TimeUnit.SECONDS);
        scheduledTasks.add(stopTask);

        // Clear tracking data but leave water blocks (permanent damage)
        scheduler.schedule(() -> {
            placedWaterBlocks.clear();

            // Final message - water stays
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§7§lTsunami has ended... §8The flooding remains.")));
            }
        }, 45L, TimeUnit.SECONDS);
    }

    public static class TsunamiEventHandler {
        @SubscribeEvent
        public static void onLivingUpdate(LivingEvent.LivingTickEvent event) {
            if (!tsunamiActive || !(event.getEntity() instanceof ServerPlayer player)) return;

            ServerLevel level = player.serverLevel();
            BlockPos playerPos = player.blockPosition();

            // Check if player is in water (EXPANDED detection)
            boolean inWater = level.getBlockState(playerPos).is(Blocks.WATER) ||
                             level.getBlockState(playerPos.above()).is(Blocks.WATER) ||
                             level.getBlockState(playerPos.below()).is(Blocks.WATER) ||
                             level.getBlockState(playerPos.north()).is(Blocks.WATER) ||
                             level.getBlockState(playerPos.south()).is(Blocks.WATER) ||
                             level.getBlockState(playerPos.east()).is(Blocks.WATER) ||
                             level.getBlockState(playerPos.west()).is(Blocks.WATER) ||
                             player.isInWater(); // Also check Minecraft's built-in water detection

            if (inWater) {
                // TSUNAMI SPIRALING PHYSICS - VERTICAL SPIRALING + DIRECTIONAL PUSHING

                // 1. STRONG VERTICAL SPIRALING - up and down motion
                double spiralTime = System.currentTimeMillis() * 0.04; // Fast spiraling
                double verticalSpiral = Math.sin(spiralTime) * 8.0; // Strong up/down motion

                // 2. DIRECTIONAL PUSHING - forward and backward forces
                double pushTime = spiralTime * 0.6; // Different rhythm for pushing
                Vec3 lookDirection = player.getLookAngle();
                double forwardPush = Math.cos(pushTime) * 10.0; // Strong directional push

                // 3. HORIZONTAL SPIRALING - side to side motion
                double horizontalSpiral = Math.cos(spiralTime * 1.8) * 5.0; // Side spiraling

                // 4. CALCULATE FINAL FORCES
                double pushX = lookDirection.x * forwardPush + horizontalSpiral;
                double pushZ = lookDirection.z * forwardPush;
                double pushY = verticalSpiral;

                // 5. APPLY TSUNAMI FORCES
                player.setDeltaMovement(pushX, pushY, pushZ);

                // 6. MODERATE view rotation for realism
                player.setYRot(player.getYRot() + 45); // Moderate rotation
                player.setXRot(player.getXRot() + (float)(Math.random() - 0.5) * 90); // Moderate pitch

                // 7. Show tsunami messages
                if (Math.random() < 0.15) { // 15% chance per tick
                    String[] tsunamiMessages = {
                        "§1§lSpiraling UP and DOWN in the tsunami!",
                        "§9§lWave pushing you forward and backward!",
                        "§1§lCaught in the tsunami's vertical spiral!",
                        "§9§lMassive wave forces tossing you around!"
                    };
                    String message = tsunamiMessages[(int)(Math.random() * tsunamiMessages.length)];
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                        net.minecraft.network.chat.Component.literal(message)));
                }
            }
        }
    }
}
