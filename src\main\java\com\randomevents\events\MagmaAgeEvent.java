package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class MagmaAgeEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    
    @Override
    public String getId() {
        return "magma_age";
    }
    
    @Override
    public String getName() {
        return "§c§lMagma Age!";
    }
    
    @Override
    public String getDescription() {
        return "Massive magma age covers 500x500 area - ground and water turn to magma instantly";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§c§lMAGMA AGE! §7The ground turns to molten rock!")));
        }

        // Spread magma around each player in expanding rings over 30 seconds
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            spreadMagmaAroundPlayer(player);
        }
    }

    private void spreadMagmaAroundPlayer(ServerPlayer player) {
        ServerLevel level = player.serverLevel();
        BlockPos playerPos = player.blockPosition();

        // Spread magma in expanding rings over 30 seconds
        // Start with radius 1 and expand to radius 50 over 30 seconds
        for (int wave = 1; wave <= 30; wave++) {
            final int currentWave = wave;

            scheduler.schedule(() -> {
                // Calculate radius for this wave (accelerating expansion)
                int radius = (int) Math.pow(currentWave * 1.8, 1.2); // Accelerating growth
                if (radius > 50) radius = 50; // Cap at 50 blocks

                // Spread magma in a ring pattern
                spreadMagmaRing(level, playerPos, radius);

            }, wave * 1L, TimeUnit.SECONDS); // Every second for 30 seconds
        }
    }

    private void spreadMagmaRing(ServerLevel level, BlockPos centerPos, int radius) {
        // Create magma in a ring pattern around the center
        for (int x = -radius; x <= radius; x++) {
            for (int z = -radius; z <= radius; z++) {
                // Only process blocks at the edge of the current radius (ring effect)
                double distance = Math.sqrt(x * x + z * z);
                if (distance >= radius - 2 && distance <= radius) { // 2-block thick ring

                    // Check ground level blocks
                    for (int y = -3; y <= 2; y++) {
                        BlockPos checkPos = centerPos.offset(x, y, z);

                        if (level.isInWorldBounds(checkPos)) {
                            BlockState blockState = level.getBlockState(checkPos);

                            // Turn water into magma blocks
                            if (blockState.is(Blocks.WATER)) {
                                level.setBlock(checkPos, Blocks.MAGMA_BLOCK.defaultBlockState(), 3);
                            }
                            // Turn ground blocks into magma (only at ground level)
                            else if (y <= 1 && (blockState.is(Blocks.STONE) || blockState.is(Blocks.DIRT) ||
                                               blockState.is(Blocks.GRASS_BLOCK) || blockState.is(Blocks.COBBLESTONE) ||
                                               blockState.is(Blocks.SAND) || blockState.is(Blocks.GRAVEL) ||
                                               blockState.is(Blocks.ANDESITE) || blockState.is(Blocks.GRANITE) ||
                                               blockState.is(Blocks.DIORITE) || blockState.is(Blocks.DEEPSLATE) ||
                                               blockState.is(Blocks.NETHERRACK) || blockState.is(Blocks.END_STONE) ||
                                               blockState.is(Blocks.SANDSTONE) || blockState.is(Blocks.RED_SANDSTONE) ||
                                               blockState.is(Blocks.ICE))) { // Also convert ice to magma
                                level.setBlock(checkPos, Blocks.MAGMA_BLOCK.defaultBlockState(), 3);
                            }
                        }
                    }
                }
            }
        }
    }
}
