package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;

public class MagmaAgeEvent extends RandomEvent {
    
    @Override
    public String getId() {
        return "magma_age";
    }
    
    @Override
    public String getName() {
        return "§c§lMagma Age!";
    }
    
    @Override
    public String getDescription() {
        return "Massive magma age covers 500x500 area - ground and water turn to magma instantly";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            ServerLevel level = player.serverLevel();
            BlockPos playerPos = player.blockPosition();

            // Turn the MASSIVE ground under and around players into magma (500x500 area!)
            for (int x = -250; x <= 250; x++) {
                for (int z = -250; z <= 250; z++) {
                    // Check the floor level (blocks under the player) - wider Y range
                    for (int y = -10; y <= 5; y++) {
                        BlockPos checkPos = playerPos.offset(x, y, z);

                        if (level.isInWorldBounds(checkPos)) {
                            BlockState blockState = level.getBlockState(checkPos);

                            // Turn water into magma blocks
                            if (blockState.is(Blocks.WATER)) {
                                level.setBlock(checkPos, Blocks.MAGMA_BLOCK.defaultBlockState(), 3);
                            }
                            // Turn ALL solid ground blocks into magma - 100% chance for fast coverage
                            else if (y <= 3 && (blockState.is(Blocks.STONE) || blockState.is(Blocks.DIRT) ||
                                               blockState.is(Blocks.GRASS_BLOCK) || blockState.is(Blocks.COBBLESTONE) ||
                                               blockState.is(Blocks.SAND) || blockState.is(Blocks.GRAVEL) ||
                                               blockState.is(Blocks.ANDESITE) || blockState.is(Blocks.GRANITE) ||
                                               blockState.is(Blocks.DIORITE) || blockState.is(Blocks.DEEPSLATE) ||
                                               blockState.is(Blocks.NETHERRACK) || blockState.is(Blocks.END_STONE) ||
                                               blockState.is(Blocks.SANDSTONE) || blockState.is(Blocks.RED_SANDSTONE) ||
                                               blockState.is(Blocks.ICE))) { // Also convert ice to magma
                                // 100% chance to turn ground blocks to magma for faster coverage
                                level.setBlock(checkPos, Blocks.MAGMA_BLOCK.defaultBlockState(), 3);
                            }
                        }
                    }
                }
            }

            // Also turn any water in an even larger radius into magma (600x600!)
            for (int x = -300; x <= 300; x++) {
                for (int y = -50; y <= 50; y++) {
                    for (int z = -300; z <= 300; z++) {
                        BlockPos checkPos = playerPos.offset(x, y, z);

                        // Check if it's within the world bounds
                        if (level.isInWorldBounds(checkPos)) {
                            if (level.getBlockState(checkPos).is(Blocks.WATER)) {
                                level.setBlock(checkPos, Blocks.MAGMA_BLOCK.defaultBlockState(), 3);
                            }
                        }
                    }
                }
            }
        }
    }
}
