package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;

public class MagmaAgeEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private final List<ScheduledFuture<?>> scheduledTasks = new ArrayList<>();
    private final Map<ServerPlayer, BlockPos> playerLastPositions = new HashMap<>();
    private static volatile boolean magmaChaseActive = false;
    
    @Override
    public String getId() {
        return "magma_age";
    }
    
    @Override
    public String getName() {
        return "§c§lMagma Age!";
    }
    
    @Override
    public String getDescription() {
        return "Massive magma age covers 500x500 area - ground and water turn to magma instantly";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        scheduledTasks.clear();
        playerLastPositions.clear();
        magmaChaseActive = true;

        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§c§lMagma Age! §7Magma blocks are chasing you! Keep running!")));

            // Store initial player position
            playerLastPositions.put(player, player.blockPosition());
        }

        // Start magma chase - check every 1 second for 30 seconds (reduced frequency for stability)
        ScheduledFuture<?> chaseTask = scheduler.scheduleAtFixedRate(() -> {
            if (!magmaChaseActive) return;

            try {
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    if (player.isAlive()) {
                        ServerLevel level = player.serverLevel();
                        BlockPos currentPos = player.blockPosition();
                        BlockPos lastPos = playerLastPositions.get(player);

                        if (lastPos != null) {
                            // Place magma blocks behind the player (where they were)
                            placeMagmaBehindPlayer(level, lastPos, currentPos);
                        }

                        // Update player's last position
                        playerLastPositions.put(player, currentPos);
                    }
                }
            } catch (Exception e) {
                // Ignore errors to prevent crashes
            }
        }, 1000L, 1000L, TimeUnit.MILLISECONDS); // Every 1 second (safer)
        scheduledTasks.add(chaseTask);

        // Stop event after exactly 30 seconds
        ScheduledFuture<?> stopTask = scheduler.schedule(() -> {
            magmaChaseActive = false;

            // Cancel all scheduled tasks
            for (ScheduledFuture<?> task : scheduledTasks) {
                if (!task.isDone()) {
                    task.cancel(false);
                }
            }
            scheduledTasks.clear();
            playerLastPositions.clear();

            // Show final message
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§7§lMagma chase has ended! §c§lThe magma blocks remain...")));
            }
        }, 30L, TimeUnit.SECONDS);
        scheduledTasks.add(stopTask);
    }

    private void placeMagmaBehindPlayer(ServerLevel level, BlockPos lastPos, BlockPos currentPos) {
        try {
            // Calculate direction player moved
            int deltaX = currentPos.getX() - lastPos.getX();
            int deltaZ = currentPos.getZ() - lastPos.getZ();

            // Place magma in a smaller area behind the player (5x5 area, reduced for stability)
            for (int x = -2; x <= 2; x++) {
                for (int z = -2; z <= 2; z++) {
                    // Focus on area behind player's movement
                    BlockPos magmaPos = new BlockPos(
                        lastPos.getX() + x - deltaX, // Opposite direction of movement
                        lastPos.getY() - 1, // Floor level
                        lastPos.getZ() + z - deltaZ  // Opposite direction of movement
                    );

                    if (level.isInWorldBounds(magmaPos)) {
                        BlockState blockState = level.getBlockState(magmaPos);

                        // Turn ground blocks into magma (reduced block types for performance)
                        if (blockState.is(Blocks.STONE) || blockState.is(Blocks.DIRT) ||
                            blockState.is(Blocks.GRASS_BLOCK) || blockState.is(Blocks.SAND) ||
                            blockState.is(Blocks.WATER) || blockState.is(Blocks.ICE)) {

                            level.setBlock(magmaPos, Blocks.MAGMA_BLOCK.defaultBlockState(), 2);
                        }
                    }
                }
            }

            // Also place some magma at the exact last position (3x3 area, reduced)
            for (int x = -1; x <= 1; x++) {
                for (int z = -1; z <= 1; z++) {
                    BlockPos exactPos = new BlockPos(lastPos.getX() + x, lastPos.getY() - 1, lastPos.getZ() + z);

                    if (level.isInWorldBounds(exactPos)) {
                        BlockState blockState = level.getBlockState(exactPos);

                        if (blockState.is(Blocks.STONE) || blockState.is(Blocks.DIRT) ||
                            blockState.is(Blocks.GRASS_BLOCK) || blockState.is(Blocks.SAND) ||
                            blockState.is(Blocks.WATER) || blockState.is(Blocks.ICE)) {

                            level.setBlock(exactPos, Blocks.MAGMA_BLOCK.defaultBlockState(), 2);
                        }
                    }
                }
            }
        } catch (Exception e) {
            // Ignore block update errors to prevent crashes
        }
    }
}
