package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;

public class MagmaAgeEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private final List<ScheduledFuture<?>> scheduledTasks = new ArrayList<>();
    private final Map<ServerPlayer, BlockPos> playerLastPositions = new HashMap<>();
    private static volatile boolean magmaChaseActive = false;
    
    @Override
    public String getId() {
        return "magma_age";
    }
    
    @Override
    public String getName() {
        return "§c§lMagma Age!";
    }
    
    @Override
    public String getDescription() {
        return "Massive magma age covers 500x500 area - ground and water turn to magma instantly";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        scheduledTasks.clear();
        playerLastPositions.clear();
        magmaChaseActive = true;

        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§c§lMagma Age! §7Magma blocks are chasing you! Keep running!")));

            // Store initial player position
            playerLastPositions.put(player, player.blockPosition());
        }

        // Start magma chase - check every 0.5 seconds for 20 seconds
        ScheduledFuture<?> chaseTask = scheduler.scheduleAtFixedRate(() -> {
            if (!magmaChaseActive) return;

            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                ServerLevel level = player.serverLevel();
                BlockPos currentPos = player.blockPosition();
                BlockPos lastPos = playerLastPositions.get(player);

                if (lastPos != null) {
                    // Place magma blocks behind the player (where they were)
                    placeMagmaBehindPlayer(level, lastPos, currentPos);
                }

                // Update player's last position
                playerLastPositions.put(player, currentPos);
            }
        }, 500L, 500L, TimeUnit.MILLISECONDS); // Every 0.5 seconds
        scheduledTasks.add(chaseTask);

        // Stop event after exactly 20 seconds
        ScheduledFuture<?> stopTask = scheduler.schedule(() -> {
            magmaChaseActive = false;

            // Cancel all scheduled tasks
            for (ScheduledFuture<?> task : scheduledTasks) {
                if (!task.isDone()) {
                    task.cancel(false);
                }
            }
            scheduledTasks.clear();
            playerLastPositions.clear();

            // Show final message
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§7§lMagma chase has ended! §c§lThe magma blocks remain...")));
            }
        }, 20L, TimeUnit.SECONDS);
        scheduledTasks.add(stopTask);
    }

    private void placeMagmaBehindPlayer(ServerLevel level, BlockPos lastPos, BlockPos currentPos) {
        // Calculate direction player moved
        int deltaX = currentPos.getX() - lastPos.getX();
        int deltaZ = currentPos.getZ() - lastPos.getZ();

        // Place magma in a wider area behind the player (7x7 area for wider range)
        for (int x = -3; x <= 3; x++) {
            for (int z = -3; z <= 3; z++) {
                // Focus on area behind player's movement
                BlockPos magmaPos = new BlockPos(
                    lastPos.getX() + x - deltaX, // Opposite direction of movement
                    lastPos.getY() - 1, // Floor level
                    lastPos.getZ() + z - deltaZ  // Opposite direction of movement
                );

                if (level.isInWorldBounds(magmaPos)) {
                    BlockState blockState = level.getBlockState(magmaPos);

                    // Turn ground blocks into magma
                    if (blockState.is(Blocks.STONE) || blockState.is(Blocks.DIRT) ||
                        blockState.is(Blocks.GRASS_BLOCK) || blockState.is(Blocks.COBBLESTONE) ||
                        blockState.is(Blocks.SAND) || blockState.is(Blocks.GRAVEL) ||
                        blockState.is(Blocks.ANDESITE) || blockState.is(Blocks.GRANITE) ||
                        blockState.is(Blocks.DIORITE) || blockState.is(Blocks.DEEPSLATE) ||
                        blockState.is(Blocks.NETHERRACK) || blockState.is(Blocks.END_STONE) ||
                        blockState.is(Blocks.SANDSTONE) || blockState.is(Blocks.RED_SANDSTONE) ||
                        blockState.is(Blocks.WATER) || blockState.is(Blocks.ICE)) {

                        level.setBlock(magmaPos, Blocks.MAGMA_BLOCK.defaultBlockState(), 2);
                    }
                }
            }
        }

        // Also place some magma at the exact last position (5x5 area)
        for (int x = -2; x <= 2; x++) {
            for (int z = -2; z <= 2; z++) {
                BlockPos exactPos = new BlockPos(lastPos.getX() + x, lastPos.getY() - 1, lastPos.getZ() + z);

                if (level.isInWorldBounds(exactPos)) {
                    BlockState blockState = level.getBlockState(exactPos);

                    if (blockState.is(Blocks.STONE) || blockState.is(Blocks.DIRT) ||
                        blockState.is(Blocks.GRASS_BLOCK) || blockState.is(Blocks.COBBLESTONE) ||
                        blockState.is(Blocks.SAND) || blockState.is(Blocks.GRAVEL) ||
                        blockState.is(Blocks.WATER) || blockState.is(Blocks.ICE)) {

                        level.setBlock(exactPos, Blocks.MAGMA_BLOCK.defaultBlockState(), 2);
                    }
                }
            }
        }
    }
}
