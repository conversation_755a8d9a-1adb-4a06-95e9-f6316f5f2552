package com.randomevents.events;

import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class DiamondDowngradeEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    
    @Override
    public String getId() {
        return "diamond_downgrade";
    }
    
    @Override
    public String getName() {
        return "§b§lDiamond Downgrade!";
    }
    
    @Override
    public String getDescription() {
        return "All diamond armor gets downgraded to gold armor - only triggers if someone has diamond armor";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        List<ServerPlayer> playersWithDiamond = new ArrayList<>();
        
        // First, check if anyone has diamond armor
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            if (hasDiamondArmor(player)) {
                playersWithDiamond.add(player);
            }
        }
        
        // Only proceed if someone has diamond armor
        if (playersWithDiamond.isEmpty()) {
            // This event shouldn't trigger if no one has diamond armor
            // But if it does, show a message and exit
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§7§lNo diamond armor detected... §8Event cancelled.")));
            }
            return;
        }
        
        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§b§lDiamond armor detected! §6§lDowngrading to gold...")));
        }
        
        // Wait 2 seconds then start the downgrade
        scheduler.schedule(() -> {
            int totalDowngrades = 0;

            // Downgrade all diamond armor for all players
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                int playerDowngrades = downgradeDiamondArmor(player);
                totalDowngrades += playerDowngrades;

                if (playerDowngrades > 0) {
                    // Play downgrade sound for affected players
                    player.serverLevel().playSound(null, player.blockPosition(),
                        SoundEvents.ANVIL_USE, SoundSource.PLAYERS, 1.0f, 0.8f);

                    // Show personal downgrade message
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                        net.minecraft.network.chat.Component.literal("§6§lYour diamond armor has been downgraded to gold! §7(" + playerDowngrades + " pieces)")));
                }
            }

            // Make totalDowngrades effectively final for lambda
            final int finalTotalDowngrades = totalDowngrades;

            // Show completion message
            scheduler.schedule(() -> {
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    if (finalTotalDowngrades > 0) {
                        player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                            net.minecraft.network.chat.Component.literal("§6§lDowngrade complete! §7" + finalTotalDowngrades + " diamond pieces became gold.")));
                    } else {
                        player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                            net.minecraft.network.chat.Component.literal("§7§lNo diamond armor found to downgrade.")));
                    }
                }
            }, 1L, TimeUnit.SECONDS);

        }, 2L, TimeUnit.SECONDS); // 2 second delay after warning
    }
    
    private boolean hasDiamondArmor(ServerPlayer player) {
        // Check all armor slots for diamond armor
        ItemStack helmet = player.getItemBySlot(EquipmentSlot.HEAD);
        ItemStack chestplate = player.getItemBySlot(EquipmentSlot.CHEST);
        ItemStack leggings = player.getItemBySlot(EquipmentSlot.LEGS);
        ItemStack boots = player.getItemBySlot(EquipmentSlot.FEET);
        
        return isDiamondArmor(helmet) || isDiamondArmor(chestplate) || 
               isDiamondArmor(leggings) || isDiamondArmor(boots);
    }
    
    private boolean isDiamondArmor(ItemStack itemStack) {
        if (itemStack.isEmpty()) return false;
        
        Item item = itemStack.getItem();
        return item == Items.DIAMOND_HELMET || 
               item == Items.DIAMOND_CHESTPLATE || 
               item == Items.DIAMOND_LEGGINGS || 
               item == Items.DIAMOND_BOOTS;
    }
    
    private int downgradeDiamondArmor(ServerPlayer player) {
        int downgradeCount = 0;
        
        // Check and downgrade helmet
        ItemStack helmet = player.getItemBySlot(EquipmentSlot.HEAD);
        if (helmet.getItem() == Items.DIAMOND_HELMET) {
            ItemStack goldHelmet = createGoldEquivalent(helmet, Items.GOLDEN_HELMET);
            player.setItemSlot(EquipmentSlot.HEAD, goldHelmet);
            downgradeCount++;
        }
        
        // Check and downgrade chestplate
        ItemStack chestplate = player.getItemBySlot(EquipmentSlot.CHEST);
        if (chestplate.getItem() == Items.DIAMOND_CHESTPLATE) {
            ItemStack goldChestplate = createGoldEquivalent(chestplate, Items.GOLDEN_CHESTPLATE);
            player.setItemSlot(EquipmentSlot.CHEST, goldChestplate);
            downgradeCount++;
        }
        
        // Check and downgrade leggings
        ItemStack leggings = player.getItemBySlot(EquipmentSlot.LEGS);
        if (leggings.getItem() == Items.DIAMOND_LEGGINGS) {
            ItemStack goldLeggings = createGoldEquivalent(leggings, Items.GOLDEN_LEGGINGS);
            player.setItemSlot(EquipmentSlot.LEGS, goldLeggings);
            downgradeCount++;
        }
        
        // Check and downgrade boots
        ItemStack boots = player.getItemBySlot(EquipmentSlot.FEET);
        if (boots.getItem() == Items.DIAMOND_BOOTS) {
            ItemStack goldBoots = createGoldEquivalent(boots, Items.GOLDEN_BOOTS);
            player.setItemSlot(EquipmentSlot.FEET, goldBoots);
            downgradeCount++;
        }
        
        return downgradeCount;
    }
    
    private ItemStack createGoldEquivalent(ItemStack originalDiamond, Item goldItem) {
        // Create new gold armor piece
        ItemStack goldArmor = new ItemStack(goldItem);
        
        // Try to preserve enchantments if any
        if (originalDiamond.isEnchanted()) {
            // Copy enchantments from diamond to gold
            goldArmor.setTag(originalDiamond.getTag().copy());
        }
        
        // Preserve custom name if any
        if (originalDiamond.hasCustomHoverName()) {
            goldArmor.setHoverName(originalDiamond.getHoverName());
        }
        
        // Set durability to match the percentage of the original
        if (originalDiamond.isDamaged()) {
            int originalMaxDurability = originalDiamond.getMaxDamage();
            int originalDamage = originalDiamond.getDamageValue();
            double damagePercentage = (double) originalDamage / originalMaxDurability;
            
            int goldMaxDurability = goldArmor.getMaxDamage();
            int goldDamage = (int) (damagePercentage * goldMaxDurability);
            
            goldArmor.setDamageValue(goldDamage);
        }
        
        return goldArmor;
    }
    
    // Override the canTrigger method to only allow this event if someone has diamond armor
    // But allow it to trigger after event 20 even if no one has diamond armor (to prevent getting stuck)
    public boolean canTrigger(MinecraftServer server) {
        com.randomevents.manager.RandomEventManager manager = com.randomevents.RandomEventsMod.getEventManager();
        if (manager != null) {
            int currentEventNumber = manager.getCurrentEventNumber();
            // After event 20, allow it to trigger regardless (to prevent system getting stuck)
            if (currentEventNumber >= 20) {
                return true;
            }
        }

        // Before event 20, only trigger if someone has diamond armor
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            if (hasDiamondArmor(player)) {
                return true;
            }
        }
        return false; // Don't trigger if no one has diamond armor and before event 20
    }
}
