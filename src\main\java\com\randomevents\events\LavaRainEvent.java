package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.level.block.Blocks;

import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class LavaRainEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    
    @Override
    public String getId() {
        return "lava_rain";
    }
    
    @Override
    public String getName() {
        return "§6§lLava Rain!";
    }
    
    @Override
    public String getDescription() {
        return "Lava blocks fall from sky around players";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        // Drop lava blocks for 15 seconds
        for (int i = 0; i < 15; i++) {
            scheduler.schedule(() -> {
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    ServerLevel level = player.serverLevel();
                    BlockPos playerPos = player.blockPosition();
                    
                    // Drop 3-5 lava blocks around the player
                    int lavaCount = 3 + random.nextInt(3);
                    for (int j = 0; j < lavaCount; j++) {
                        int x = playerPos.getX() + random.nextInt(20) - 10;
                        int z = playerPos.getZ() + random.nextInt(20) - 10;
                        int y = level.getHeight(net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING, x, z) + 10;
                        
                        BlockPos lavaPos = new BlockPos(x, y, z);
                        
                        // Only place if the position is air
                        if (level.getBlockState(lavaPos).isAir()) {
                            // Use flag 2 to prevent block updates and reduce tick load
                            level.setBlock(lavaPos, Blocks.LAVA.defaultBlockState(), 2);
                        }
                    }
                }
            }, i * 1L, TimeUnit.SECONDS);
        }
    }
}
