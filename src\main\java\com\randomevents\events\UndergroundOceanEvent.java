package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.level.block.Blocks;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.List;
import java.util.ArrayList;

public class UndergroundOceanEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private final List<ScheduledFuture<?>> scheduledTasks = new ArrayList<>();
    private static boolean oceanActive = false;

    @Override
    public String getId() {
        return "underground_ocean";
    }

    @Override
    public String getName() {
        return "§1§lUnderground Ocean!";
    }

    @Override
    public String getDescription() {
        return "Players stand on 1 block while deep water surrounds them everywhere";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        scheduledTasks.clear();
        oceanActive = true;

        // Show initial message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§1§lUnderground Ocean - blocks beneath you are turning to water!")));
        }

        // Replace blocks underneath players with water over 30 seconds
        for (int i = 0; i < 60; i++) { // 60 waves over 30 seconds (every 0.5 seconds)
            final int wave = i;
            ScheduledFuture<?> task = scheduler.schedule(() -> {
                if (!oceanActive) return; // Stop if event ended

                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    ServerLevel level = player.serverLevel();
                    BlockPos playerPos = player.blockPosition();

                    // Calculate radius for this wave (grows outward from player)
                    int radius = Math.min(wave + 5, 30); // Start at 5, grow to max 30

                    // Replace blocks underneath player in expanding circle
                    for (int x = -radius; x <= radius; x++) {
                        for (int z = -radius; z <= radius; z++) {
                            // Only affect blocks in circular pattern
                            if (x * x + z * z <= radius * radius) {
                                // Replace blocks underneath (5-15 blocks deep)
                                for (int y = 5; y <= 15; y++) {
                                    BlockPos waterPos = new BlockPos(playerPos.getX() + x, playerPos.getY() - y, playerPos.getZ() + z);

                                    if (level.isInWorldBounds(waterPos)) {
                                        // Replace blocks with water (except important blocks)
                                        if (!level.getBlockState(waterPos).is(Blocks.BEDROCK) &&
                                            !level.getBlockState(waterPos).is(Blocks.SPAWNER) &&
                                            !level.getBlockState(waterPos).is(Blocks.END_PORTAL) &&
                                            !level.getBlockState(waterPos).is(Blocks.END_PORTAL_FRAME)) {
                                            level.setBlock(waterPos, Blocks.WATER.defaultBlockState(), 3);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }, i * 500L, TimeUnit.MILLISECONDS); // Every 0.5 seconds
            scheduledTasks.add(task);
        }

        // Stop event after exactly 30 seconds
        ScheduledFuture<?> stopTask = scheduler.schedule(() -> {
            oceanActive = false;

            // Cancel all remaining scheduled tasks
            for (ScheduledFuture<?> task : scheduledTasks) {
                if (!task.isDone()) {
                    task.cancel(false);
                }
            }
            scheduledTasks.clear();
        }, 30L, TimeUnit.SECONDS);
        scheduledTasks.add(stopTask);
    }
}
