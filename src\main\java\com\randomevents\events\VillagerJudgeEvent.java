package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.npc.Villager;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class VillagerJudgeEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final List<Villager> spawnedVillagers = new ArrayList<>();
    
    @Override
    public String getId() {
        return "villager_judge";
    }
    
    @Override
    public String getName() {
        return "§6§lVillager Judge";
    }
    
    @Override
    public String getDescription() {
        return "A single villager spawns and stares at the player silently... then starts chasing after 5 seconds!";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        spawnedVillagers.clear();
        
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            ServerLevel level = player.serverLevel();
            BlockPos playerPos = player.blockPosition();

            // Calculate position 5 blocks in front of player based on their facing direction
            net.minecraft.world.phys.Vec3 lookDirection = player.getLookAngle();
            double villagerX = playerPos.getX() + (lookDirection.x * 5);
            double villagerY = playerPos.getY();
            double villagerZ = playerPos.getZ() + (lookDirection.z * 5);

            Villager villager = EntityType.VILLAGER.create(level);
            if (villager != null) {
                villager.moveTo(villagerX, villagerY, villagerZ);

                // Make villager invulnerable and unable to move
                villager.setInvulnerable(true);
                villager.setNoAi(true);

                // Calculate the angle to face the player
                double deltaX = playerPos.getX() - villagerX;
                double deltaZ = playerPos.getZ() - villagerZ;
                float yaw = (float) (Math.atan2(deltaZ, deltaX) * 180.0 / Math.PI) - 90.0f;

                // Set villager rotation to face the player
                villager.setYRot(yaw);
                villager.setYHeadRot(yaw);
                villager.yRotO = yaw;
                villager.yHeadRotO = yaw;

                level.addFreshEntity(villager);
                spawnedVillagers.add(villager);

                // Show message
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§6§lA villager is judging you... §7*stares silently*")));

                // Play eerie villager sound when spawning
                level.playSound(null, villager.blockPosition(), SoundEvents.VILLAGER_AMBIENT, SoundSource.NEUTRAL, 1.0f, 0.7f);
            }
        }

        // After 5 seconds, start the chase!
        scheduler.schedule(() -> {
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§c§lTHE VILLAGER IS COMING FOR YOU! §4§lRUN!")));

                // Play ominous sound when chase starts
                ServerLevel level = player.serverLevel();
                level.playSound(null, player.blockPosition(), SoundEvents.VILLAGER_AMBIENT, SoundSource.HOSTILE, 2.0f, 0.5f);
            }

            // Enable AI and make villagers chase players
            for (Villager villager : spawnedVillagers) {
                if (villager.isAlive()) {
                    villager.setNoAi(false); // Enable AI so it can move
                    villager.setInvulnerable(false); // Make it vulnerable so it feels more real

                    // Make villager faster for chasing
                    villager.getAttribute(net.minecraft.world.entity.ai.attributes.Attributes.MOVEMENT_SPEED)
                        .setBaseValue(0.7); // Faster than normal villager speed
                }
            }

            // Start chasing behavior - check every 0.5 seconds for 55 seconds (until cleanup)
            for (int i = 0; i < 110; i++) { // 110 checks over 55 seconds
                scheduler.schedule(() -> {
                    for (Villager villager : spawnedVillagers) {
                        if (villager.isAlive()) {
                            chaseNearestPlayer(villager);
                        }
                    }
                }, i * 500L, TimeUnit.MILLISECONDS); // Every 0.5 seconds
            }

        }, 5L, TimeUnit.SECONDS);

        // Remove villagers after 60 seconds
        scheduler.schedule(() -> {
            for (Villager villager : spawnedVillagers) {
                if (villager.isAlive()) {
                    villager.remove(net.minecraft.world.entity.Entity.RemovalReason.DISCARDED);
                }
            }
            spawnedVillagers.clear();
        }, 60L, TimeUnit.SECONDS);
    }

    private void chaseNearestPlayer(Villager villager) {
        ServerLevel level = (ServerLevel) villager.level();

        // Find nearest player
        ServerPlayer nearestPlayer = null;
        double nearestDistance = Double.MAX_VALUE;

        for (ServerPlayer player : level.getServer().getPlayerList().getPlayers()) {
            if (player.serverLevel() == level) {
                double distance = villager.distanceToSqr(player);
                if (distance < nearestDistance) {
                    nearestDistance = distance;
                    nearestPlayer = player;
                }
            }
        }

        // Chase the nearest player if within range
        if (nearestPlayer != null && nearestDistance < 400) { // Within 20 blocks
            net.minecraft.world.phys.Vec3 direction = nearestPlayer.position()
                .subtract(villager.position()).normalize();

            // Move villager towards player aggressively
            villager.setDeltaMovement(direction.scale(0.5)); // Aggressive chase speed

            // Make villager look at the player while chasing
            double deltaX = nearestPlayer.getX() - villager.getX();
            double deltaZ = nearestPlayer.getZ() - villager.getZ();
            float yaw = (float) (Math.atan2(deltaZ, deltaX) * 180.0 / Math.PI) - 90.0f;
            villager.setYRot(yaw);
            villager.setYHeadRot(yaw);

            // If very close, show menacing message occasionally
            if (nearestDistance < 9 && Math.random() < 0.1) { // Within 3 blocks, 10% chance
                nearestPlayer.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§4§lTHE VILLAGER IS RIGHT BEHIND YOU! §c§l*JUDGING INTENSIFIES*")));

                // Play scary villager sound when very close
                level.playSound(null, villager.blockPosition(), SoundEvents.VILLAGER_NO, SoundSource.HOSTILE, 1.5f, 0.8f);
            }
        }
    }
}
