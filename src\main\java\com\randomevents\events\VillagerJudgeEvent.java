package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.npc.Villager;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class VillagerJudgeEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final List<Villager> spawnedVillagers = new ArrayList<>();
    
    @Override
    public String getId() {
        return "villager_judge";
    }
    
    @Override
    public String getName() {
        return "§6§lVillager Judge";
    }
    
    @Override
    public String getDescription() {
        return "A single villager spawns and stares at the player silently";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        spawnedVillagers.clear();
        
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            ServerLevel level = player.serverLevel();
            BlockPos playerPos = player.blockPosition();

            // Calculate position 5 blocks in front of player based on their facing direction
            net.minecraft.world.phys.Vec3 lookDirection = player.getLookAngle();
            double villagerX = playerPos.getX() + (lookDirection.x * 5);
            double villagerY = playerPos.getY();
            double villagerZ = playerPos.getZ() + (lookDirection.z * 5);

            Villager villager = EntityType.VILLAGER.create(level);
            if (villager != null) {
                villager.moveTo(villagerX, villagerY, villagerZ);

                // Make villager invulnerable and unable to move
                villager.setInvulnerable(true);
                villager.setNoAi(true);

                // Calculate the angle to face the player
                double deltaX = playerPos.getX() - villagerX;
                double deltaZ = playerPos.getZ() - villagerZ;
                float yaw = (float) (Math.atan2(deltaZ, deltaX) * 180.0 / Math.PI) - 90.0f;

                // Set villager rotation to face the player
                villager.setYRot(yaw);
                villager.setYHeadRot(yaw);
                villager.yRotO = yaw;
                villager.yHeadRotO = yaw;

                level.addFreshEntity(villager);
                spawnedVillagers.add(villager);

                // Show message
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§6§lA villager is judging you... §7*stares silently*")));
            }
        }
        
        // Remove villagers after 60 seconds
        scheduler.schedule(() -> {
            for (Villager villager : spawnedVillagers) {
                villager.remove(net.minecraft.world.entity.Entity.RemovalReason.DISCARDED);
            }
            spawnedVillagers.clear();
        }, 60L, TimeUnit.SECONDS);
    }
}
