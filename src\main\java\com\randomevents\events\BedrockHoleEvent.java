package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.network.chat.Component;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.level.block.Blocks;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class BedrockHoleEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    
    @Override
    public String getId() {
        return "bedrock_hole";
    }
    
    @Override
    public String getName() {
        return "§8§lBedrock Hole!";
    }
    
    @Override
    public String getDescription() {
        return "Creates a hole under each player down to bedrock after a 5 second warning";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        // First, warn all players to jump forward for 5 seconds
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                Component.literal("§c§lJUMP FORWARD NOW! §7The ground beneath you will collapse in 5 seconds!")));
        }

        // Show countdown warnings
        for (int i = 1; i <= 4; i++) {
            final int timeLeft = 5 - i;
            scheduler.schedule(() -> {
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                        Component.literal("§c§l" + timeLeft + " SECONDS! §7JUMP FORWARD!")));
                }
            }, i * 1L, TimeUnit.SECONDS);
        }

        // After 5 seconds, create the holes
        scheduler.schedule(() -> {
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                ServerLevel level = player.serverLevel();
                BlockPos playerPos = player.blockPosition();

                // Create a 5x5 hole centered on the player
                for (int x = -2; x <= 2; x++) {
                    for (int z = -2; z <= 2; z++) {
                        // Create hole from current Y level down to bedrock
                        for (int y = playerPos.getY(); y >= level.getMinBuildHeight() + 1; y--) {
                            BlockPos holePos = new BlockPos(playerPos.getX() + x, y, playerPos.getZ() + z);

                            if (level.isInWorldBounds(holePos)) {
                                // Don't destroy bedrock
                                if (!level.getBlockState(holePos).is(Blocks.BEDROCK)) {
                                    // Use flag 2 to prevent block updates and reduce tick load
                                    level.setBlock(holePos, Blocks.AIR.defaultBlockState(), 2);
                                }
                            }
                        }
                    }
                }

                // Send actionbar when the hole appears
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    Component.literal("§8§lThe ground gives way beneath you!")));
            }
        }, 5L, TimeUnit.SECONDS);
    }
}
