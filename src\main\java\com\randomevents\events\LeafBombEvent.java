package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.network.chat.Component;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.item.PrimedTnt;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.LeavesBlock;
import java.util.Random;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class LeafBombEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();

    @Override
    public String getId() {
        return "leaf_bomb";
    }

    @Override
    public String getName() {
        return "§a§lLeaf Bomb!";
    }

    @Override
    public String getDescription() {
        return "Every leaf near players turns into primed TNT after a warning";
    }

    @Override
    public void execute(MinecraftServer server) {
        // First, warn all players with actionbar text (smaller)
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                Component.literal("§2§oThe leaves don't look so good... §7Something is very wrong with the trees...")));
        }

        // After 3 seconds, turn leaves into TNT (or spawn trees if no leaves found)
        scheduler.schedule(() -> {
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                ServerLevel level = player.serverLevel();
                BlockPos playerPos = player.blockPosition();

                int leavesFound = 0;

                // Check all blocks in a 30x30x30 area around the player
                for (int x = -15; x <= 15; x++) {
                    for (int y = -15; y <= 15; y++) {
                        for (int z = -15; z <= 15; z++) {
                            BlockPos checkPos = playerPos.offset(x, y, z);

                            if (level.isInWorldBounds(checkPos)) {
                                // Check if the block is a leaf block
                                if (level.getBlockState(checkPos).getBlock() instanceof LeavesBlock) {
                                    leavesFound++;
                                    // Replace the leaf with air
                                    level.setBlock(checkPos, Blocks.AIR.defaultBlockState(), 3);

                                    // Spawn primed TNT at that location
                                    PrimedTnt tnt = EntityType.TNT.create(level);
                                    if (tnt != null) {
                                        tnt.moveTo(checkPos.getX() + 0.5, checkPos.getY(), checkPos.getZ() + 0.5);
                                        tnt.setFuse(60 + level.getRandom().nextInt(40)); // 3-5 seconds fuse
                                        level.addFreshEntity(tnt);
                                    }
                                }
                            }
                        }
                    }
                }

                // If no leaves were found, spawn trees around the player then turn them into TNT!
                if (leavesFound == 0) {
                    spawnTreesAndExplode(level, playerPos);
                }
            }
        }, 3L, TimeUnit.SECONDS);
    }

    private void spawnTreesAndExplode(ServerLevel level, BlockPos playerPos) {
        // Spawn only 2 trees exactly 15 blocks away from the player
        for (int i = 0; i < 2; i++) {
            // Position trees 15 blocks away in different directions
            double angle = (Math.PI * i); // 180 degrees apart (opposite sides)
            int distance = 15; // Exactly 15 blocks away

            int x = playerPos.getX() + (int)(Math.cos(angle) * distance);
            int z = playerPos.getZ() + (int)(Math.sin(angle) * distance);
            int y = level.getHeight(net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING_NO_LEAVES, x, z);

            BlockPos treeBase = new BlockPos(x, y, z);

            // Create a simple tree structure
            createSimpleTree(level, treeBase);
        }

        // Wait 2 seconds then explode all the new leaves
        scheduler.schedule(() -> {
            for (int x = -20; x <= 20; x++) {
                for (int y = -10; y <= 20; y++) {
                    for (int z = -20; z <= 20; z++) {
                        BlockPos checkPos = playerPos.offset(x, y, z);

                        if (level.isInWorldBounds(checkPos)) {
                            // Check if the block is a leaf block
                            if (level.getBlockState(checkPos).getBlock() instanceof LeavesBlock) {
                                // Replace the leaf with air
                                level.setBlock(checkPos, Blocks.AIR.defaultBlockState(), 3);

                                // Spawn primed TNT at that location
                                PrimedTnt tnt = EntityType.TNT.create(level);
                                if (tnt != null) {
                                    tnt.moveTo(checkPos.getX() + 0.5, checkPos.getY(), checkPos.getZ() + 0.5);
                                    tnt.setFuse(40 + level.getRandom().nextInt(40)); // 2-4 seconds fuse
                                    level.addFreshEntity(tnt);
                                }
                            }
                        }
                    }
                }
            }
        }, 2L, TimeUnit.SECONDS);
    }

    private void createSimpleTree(ServerLevel level, BlockPos base) {
        // Create trunk (5-8 blocks high)
        int trunkHeight = 5 + random.nextInt(4);
        for (int y = 0; y < trunkHeight; y++) {
            BlockPos trunkPos = base.offset(0, y, 0);
            if (level.isInWorldBounds(trunkPos) && level.getBlockState(trunkPos).isAir()) {
                level.setBlock(trunkPos, Blocks.OAK_LOG.defaultBlockState(), 3);
            }
        }

        // Create leaves (3x3x3 cube at the top)
        BlockPos leafCenter = base.offset(0, trunkHeight, 0);
        for (int x = -2; x <= 2; x++) {
            for (int y = -1; y <= 2; y++) {
                for (int z = -2; z <= 2; z++) {
                    BlockPos leafPos = leafCenter.offset(x, y, z);

                    // Skip some blocks for natural look
                    if (Math.abs(x) == 2 && Math.abs(z) == 2 && random.nextInt(3) == 0) continue;

                    if (level.isInWorldBounds(leafPos) && level.getBlockState(leafPos).isAir()) {
                        level.setBlock(leafPos, Blocks.OAK_LEAVES.defaultBlockState(), 3);
                    }
                }
            }
        }
    }
}
