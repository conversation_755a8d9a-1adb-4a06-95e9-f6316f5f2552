package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.projectile.Arrow;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.List;
import java.util.ArrayList;

public class ArrowStormOceanEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private final List<ScheduledFuture<?>> scheduledTasks = new ArrayList<>();
    private static volatile boolean stormActive = false;
    
    @Override
    public String getId() {
        return "arrow_storm";
    }
    
    @Override
    public String getName() {
        return "§4§lArrow Storm!";
    }

    @Override
    public String getDescription() {
        return "MASSIVE arrow storm rains from the sky for 15 seconds!";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        scheduledTasks.clear();
        stormActive = true;

        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§4§lMassive arrow storm incoming! §7Take cover!")));
        }

        // Start arrow storm immediately (every 1 second for 15 seconds)
        ScheduledFuture<?> arrowTask = scheduler.scheduleAtFixedRate(() -> {
            if (!stormActive) return; // Stop if event ended

            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                ServerLevel level = player.serverLevel();
                BlockPos playerPos = player.blockPosition();

                // Drop 5-10 arrows around each player
                int arrowCount = 5 + ThreadLocalRandom.current().nextInt(6);

                for (int j = 0; j < arrowCount; j++) {
                    // Random position around player
                    int x = playerPos.getX() + ThreadLocalRandom.current().nextInt(40) - 20;
                    int z = playerPos.getZ() + ThreadLocalRandom.current().nextInt(40) - 20;
                    int y = playerPos.getY() + 15 + ThreadLocalRandom.current().nextInt(10);

                    // Create arrow entity
                    Arrow arrow = new Arrow(EntityType.ARROW, level);
                    arrow.moveTo(x, y, z);

                    // Make arrows fall fast and straight down
                    arrow.setDeltaMovement(
                        (ThreadLocalRandom.current().nextDouble() - 0.5) * 0.1, // Less horizontal spread
                        -1.0, // Moderate downward velocity
                        (ThreadLocalRandom.current().nextDouble() - 0.5) * 0.1  // Less horizontal spread
                    );

                    // Set arrow properties
                    arrow.setCritArrow(ThreadLocalRandom.current().nextBoolean());
                    arrow.pickup = Arrow.Pickup.ALLOWED;

                    level.addFreshEntity(arrow);
                }
            }
        }, 1L, 1L, TimeUnit.SECONDS); // Start after 1 second, repeat every 1 second
        scheduledTasks.add(arrowTask);

        // Stop event after exactly 15 seconds
        ScheduledFuture<?> stopTask = scheduler.schedule(() -> {
            stormActive = false;

            // Cancel all scheduled tasks
            for (ScheduledFuture<?> task : scheduledTasks) {
                if (!task.isDone()) {
                    task.cancel(false);
                }
            }
            scheduledTasks.clear();

            // Show final message
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§7§lArrow storm has ended! §aCollect the arrows!")));
            }
        }, 15L, TimeUnit.SECONDS);
        scheduledTasks.add(stopTask);
    }
}
