package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.projectile.Arrow;
import net.minecraft.world.level.block.Blocks;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

public class ArrowStormOceanEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    
    @Override
    public String getId() {
        return "arrow_storm_ocean";
    }
    
    @Override
    public String getName() {
        return "§4§lArrow Storm Ocean!";
    }
    
    @Override
    public String getDescription() {
        return "Deep water everywhere with NO blocks under players + MASSIVE arrow storm from sky!";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§1§lCreating deep ocean... §4§lArrow storm incoming!")));
        }
        
        // Phase 1: Create smaller ocean gradually (prevent server freeze)
        scheduler.schedule(() -> {
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                ServerLevel level = player.serverLevel();
                BlockPos playerPos = player.blockPosition();

                // Create smaller ocean (40x40 area, 20 blocks deep) to prevent server lag
                int startY = Math.max(playerPos.getY() - 20, level.getMinBuildHeight());
                int endY = playerPos.getY() - 1; // Water goes up to just below player

                // Fill water in smaller area around the player
                for (int x = -20; x <= 20; x++) {
                    for (int z = -20; z <= 20; z++) {
                        for (int y = startY; y <= endY; y++) {
                            BlockPos waterPos = new BlockPos(playerPos.getX() + x, y, playerPos.getZ() + z);

                            if (level.isInWorldBounds(waterPos)) {
                                // Replace blocks with water (except important blocks)
                                if (!level.getBlockState(waterPos).is(Blocks.BEDROCK) &&
                                    !level.getBlockState(waterPos).is(Blocks.SPAWNER) &&
                                    !level.getBlockState(waterPos).is(Blocks.END_PORTAL) &&
                                    !level.getBlockState(waterPos).is(Blocks.END_PORTAL_FRAME)) {
                                    level.setBlock(waterPos, Blocks.WATER.defaultBlockState(), 3);
                                }
                            }
                        }
                    }
                }

                // Show ocean creation complete message
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§1§lYou're floating in DEEP water! §4§lArrow storm starting!")));
            }
        }, 1L, TimeUnit.SECONDS); // 1 second delay to prevent immediate freeze
        
        // Phase 2: Start MASSIVE arrow storm after ocean is created (every 0.2 seconds for 45 seconds)
        for (int i = 0; i < 225; i++) { // 45 seconds / 0.2 seconds = 225 arrow drops
            final int dropIndex = i; // Make variable final for lambda
            scheduler.schedule(() -> {
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    ServerLevel level = player.serverLevel();
                    BlockPos playerPos = player.blockPosition();
                    
                    // Drop 20-40 arrows around each player (MASSIVE amount!)
                    int arrowCount = 20 + ThreadLocalRandom.current().nextInt(21);
                    
                    for (int j = 0; j < arrowCount; j++) {
                        // Random position around player (large area)
                        int x = playerPos.getX() + ThreadLocalRandom.current().nextInt(80) - 40;
                        int z = playerPos.getZ() + ThreadLocalRandom.current().nextInt(80) - 40;
                        int y = playerPos.getY() + 25 + ThreadLocalRandom.current().nextInt(20); // High in the sky
                        
                        // Create arrow entity
                        Arrow arrow = new Arrow(EntityType.ARROW, level);
                        arrow.moveTo(x, y, z);
                        
                        // Make arrows fall fast and straight down
                        arrow.setDeltaMovement(
                            (ThreadLocalRandom.current().nextDouble() - 0.5) * 0.2, // Slight horizontal spread
                            -1.5, // Fast downward velocity
                            (ThreadLocalRandom.current().nextDouble() - 0.5) * 0.2  // Slight horizontal spread
                        );
                        
                        // Set arrow properties
                        arrow.setCritArrow(ThreadLocalRandom.current().nextBoolean()); // Random critical arrows
                        arrow.pickup = Arrow.Pickup.ALLOWED; // Players can pick up arrows
                        
                        level.addFreshEntity(arrow);
                    }
                }
                
                // Show arrow storm messages only occasionally (not spamming)
                if (dropIndex % 50 == 0) { // Every 10 seconds (50 * 0.2 seconds = 10 seconds)
                    for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                        String[] arrowMessages = {
                            "§4§lARROWS EVERYWHERE!",
                            "§c§lMassive arrow storm!",
                            "§4§lArrows raining from the sky!",
                            "§c§lDodge the arrow barrage!",
                            "§4§lArrow apocalypse!",
                            "§c§lThe sky is full of arrows!",
                            "§4§lArrow storm CHAOS!",
                            "§c§lSwim and dodge!"
                        };
                        String message = arrowMessages[dropIndex / 50 % arrowMessages.length]; // Cycle through messages
                        player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                            net.minecraft.network.chat.Component.literal(message)));
                    }
                }
            }, (2000 + i * 200L), TimeUnit.MILLISECONDS); // Start after 2 seconds (after ocean creation)
        }
        
        // Show final message after arrow storm ends
        scheduler.schedule(() -> {
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§7§lArrow storm has ended! §1§lYou're still in deep water though...")));
            }
        }, 45L, TimeUnit.SECONDS);
    }
}
