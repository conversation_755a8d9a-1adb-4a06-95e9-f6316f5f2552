package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.projectile.Arrow;
import net.minecraft.world.level.block.Blocks;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.List;
import java.util.ArrayList;

public class ArrowStormOceanEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private final List<ScheduledFuture<?>> scheduledTasks = new ArrayList<>();
    private static volatile boolean stormActive = false;
    
    @Override
    public String getId() {
        return "arrow_storm_ocean";
    }
    
    @Override
    public String getName() {
        return "§4§lArrow Storm Ocean!";
    }
    
    @Override
    public String getDescription() {
        return "Deep water everywhere with NO blocks under players + MASSIVE arrow storm from sky!";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        scheduledTasks.clear();
        stormActive = true;

        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§1§lCreating deep ocean... §4§lArrow storm incoming!")));
        }

        // Phase 1: Create smaller ocean (reduced size for stability)
        ScheduledFuture<?> oceanTask = scheduler.schedule(() -> {
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                ServerLevel level = player.serverLevel();
                BlockPos playerPos = player.blockPosition();

                // Create smaller ocean (20x20 area, 10 blocks deep) to prevent server lag
                int startY = Math.max(playerPos.getY() - 10, level.getMinBuildHeight());
                int endY = playerPos.getY() - 1; // Water goes up to just below player

                // Fill water in smaller area around the player
                for (int x = -10; x <= 10; x++) {
                    for (int z = -10; z <= 10; z++) {
                        for (int y = startY; y <= endY; y++) {
                            BlockPos waterPos = new BlockPos(playerPos.getX() + x, y, playerPos.getZ() + z);

                            if (level.isInWorldBounds(waterPos)) {
                                // Replace blocks with water (except important blocks) - use flag 2 for stability
                                if (!level.getBlockState(waterPos).is(Blocks.BEDROCK) &&
                                    !level.getBlockState(waterPos).is(Blocks.SPAWNER) &&
                                    !level.getBlockState(waterPos).is(Blocks.END_PORTAL) &&
                                    !level.getBlockState(waterPos).is(Blocks.END_PORTAL_FRAME)) {
                                    level.setBlock(waterPos, Blocks.WATER.defaultBlockState(), 2);
                                }
                            }
                        }
                    }
                }

                // Show ocean creation complete message
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§1§lYou're floating in DEEP water! §4§lArrow storm starting!")));
            }
        }, 1L, TimeUnit.SECONDS);
        scheduledTasks.add(oceanTask);

        // Phase 2: Start controlled arrow storm (every 1 second for 30 seconds)
        ScheduledFuture<?> arrowTask = scheduler.scheduleAtFixedRate(() -> {
            if (!stormActive) return; // Stop if event ended

            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                ServerLevel level = player.serverLevel();
                BlockPos playerPos = player.blockPosition();

                // Drop 5-10 arrows around each player (reduced from 20-40)
                int arrowCount = 5 + ThreadLocalRandom.current().nextInt(6);

                for (int j = 0; j < arrowCount; j++) {
                    // Random position around player (smaller area)
                    int x = playerPos.getX() + ThreadLocalRandom.current().nextInt(40) - 20;
                    int z = playerPos.getZ() + ThreadLocalRandom.current().nextInt(40) - 20;
                    int y = playerPos.getY() + 15 + ThreadLocalRandom.current().nextInt(10); // Lower height

                    // Create arrow entity
                    Arrow arrow = new Arrow(EntityType.ARROW, level);
                    arrow.moveTo(x, y, z);

                    // Make arrows fall fast and straight down
                    arrow.setDeltaMovement(
                        (ThreadLocalRandom.current().nextDouble() - 0.5) * 0.1, // Less horizontal spread
                        -1.0, // Moderate downward velocity
                        (ThreadLocalRandom.current().nextDouble() - 0.5) * 0.1  // Less horizontal spread
                    );

                    // Set arrow properties
                    arrow.setCritArrow(ThreadLocalRandom.current().nextBoolean());
                    arrow.pickup = Arrow.Pickup.ALLOWED;

                    level.addFreshEntity(arrow);
                }
            }
        }, 2L, 1L, TimeUnit.SECONDS); // Start after 2 seconds, repeat every 1 second
        scheduledTasks.add(arrowTask);

        // Stop event after exactly 30 seconds
        ScheduledFuture<?> stopTask = scheduler.schedule(() -> {
            stormActive = false;

            // Cancel all scheduled tasks
            for (ScheduledFuture<?> task : scheduledTasks) {
                if (!task.isDone()) {
                    task.cancel(false);
                }
            }
            scheduledTasks.clear();

            // Show final message
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§7§lArrow storm has ended! §1§lYou're still in deep water though...")));
            }
        }, 30L, TimeUnit.SECONDS);
        scheduledTasks.add(stopTask);
    }
}
