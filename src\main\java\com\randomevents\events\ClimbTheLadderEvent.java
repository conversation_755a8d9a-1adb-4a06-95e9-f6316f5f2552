package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.ChestBlock;
import net.minecraft.world.level.block.entity.ChestBlockEntity;
import net.minecraft.world.level.block.state.BlockState;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class ClimbTheLadderEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    private static final List<BlockPos> spawnedStructures = new ArrayList<>();
    
    @Override
    public String getId() {
        return "climb_the_ladder";
    }
    
    @Override
    public String getName() {
        return "§6§lClimb the Ladder!";
    }
    
    @Override
    public String getDescription() {
        return "Tall ladder structures with treasure chests spawn near players - climb for loot!";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        spawnedStructures.clear();
        
        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§6§lTall structures are appearing! §7Climb them for treasure!")));
        }
        
        // Wait 2 seconds then spawn structures
        scheduler.schedule(() -> {
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                ServerLevel level = player.serverLevel();
                BlockPos playerPos = player.blockPosition();
                
                // Find a good spot to spawn the structure (10-20 blocks away from player)
                BlockPos spawnPos = findGoodSpawnLocation(level, playerPos);
                
                if (spawnPos != null) {
                    buildLadderStructure(level, spawnPos);
                    
                    // Play structure spawn sound
                    level.playSound(null, spawnPos, SoundEvents.WOOD_PLACE, SoundSource.BLOCKS, 2.0f, 1.0f);
                    
                    // Show message to player
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                        net.minecraft.network.chat.Component.literal("§6§lA ladder structure appeared nearby! §7Climb it for treasure!")));
                }
            }
        }, 2L, TimeUnit.SECONDS);
        
        // Remove structures after 5 minutes
        scheduler.schedule(() -> {
            cleanupStructures(server);
        }, 300L, TimeUnit.SECONDS);
    }
    
    private BlockPos findGoodSpawnLocation(ServerLevel level, BlockPos playerPos) {
        // Try to find a good spot 10-20 blocks away from player
        for (int attempts = 0; attempts < 20; attempts++) {
            int x = playerPos.getX() + (random.nextInt(21) - 10) + (random.nextBoolean() ? 10 : -10); // 10-20 blocks away
            int z = playerPos.getZ() + (random.nextInt(21) - 10) + (random.nextBoolean() ? 10 : -10);
            
            // Find ground level
            int y = level.getHeight(net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING_NO_LEAVES, x, z);
            BlockPos testPos = new BlockPos(x, y, z);
            
            // Check if there's enough space (15 blocks high)
            boolean hasSpace = true;
            for (int checkY = y; checkY < y + 15; checkY++) {
                BlockPos checkPos = new BlockPos(x, checkY, z);
                if (!level.isInWorldBounds(checkPos) || !level.getBlockState(checkPos).isAir()) {
                    hasSpace = false;
                    break;
                }
            }
            
            if (hasSpace) {
                return testPos;
            }
        }
        
        // Fallback: spawn near player if no good spot found
        return playerPos.offset(15, 0, 0);
    }
    
    private void buildLadderStructure(ServerLevel level, BlockPos basePos) {
        // Build a 2x1 oak wood tower (15 blocks high) so ladder can be properly attached
        for (int y = 0; y < 15; y++) {
            // Main wood tower
            BlockPos towerPos = basePos.offset(0, y, 0);
            level.setBlock(towerPos, Blocks.OAK_WOOD.defaultBlockState(), 3);
            spawnedStructures.add(towerPos);

            // Add ladder directly attached to the wood (same X/Z, facing south)
            BlockPos ladderPos = basePos.offset(0, y, 0);
            // Place air first to ensure clean placement
            level.setBlock(ladderPos.south(), Blocks.AIR.defaultBlockState(), 3);
            // Then place ladder facing north (attached to wood)
            BlockState ladderState = Blocks.LADDER.defaultBlockState()
                .setValue(net.minecraft.world.level.block.LadderBlock.FACING, net.minecraft.core.Direction.NORTH);
            level.setBlock(ladderPos.south(), ladderState, 3);
            spawnedStructures.add(ladderPos.south());
        }

        // Place chest on top of the tower
        BlockPos chestPos = basePos.offset(0, 15, 0);
        level.setBlock(chestPos, Blocks.CHEST.defaultBlockState(), 3);
        spawnedStructures.add(chestPos);

        // Fill chest with good loot (delay to ensure chest entity is created)
        scheduler.schedule(() -> {
            // Force block update to ensure chest entity exists
            level.sendBlockUpdated(chestPos, Blocks.CHEST.defaultBlockState(), Blocks.CHEST.defaultBlockState(), 3);
            fillTreasureChest(level, chestPos);
        }, 200L, TimeUnit.MILLISECONDS); // Slightly longer delay

        // Update message to mention the chest
        for (ServerPlayer player : level.getServer().getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§6§lClimb the ladder and look in the chest!")));
        }
    }
    
    private void fillTreasureChest(ServerLevel level, BlockPos chestPos) {
        if (level.getBlockEntity(chestPos) instanceof ChestBlockEntity chest) {
            // Clear chest first
            chest.clearContent();
            
            // Add good loot items
            List<ItemStack> lootItems = new ArrayList<>();
            lootItems.add(new ItemStack(Items.DIAMOND, 2 + random.nextInt(4))); // 2-5 diamonds
            lootItems.add(new ItemStack(Items.EMERALD, 3 + random.nextInt(5))); // 3-7 emeralds
            lootItems.add(new ItemStack(Items.GOLD_INGOT, 4 + random.nextInt(8))); // 4-11 gold
            lootItems.add(new ItemStack(Items.IRON_INGOT, 6 + random.nextInt(10))); // 6-15 iron
            lootItems.add(new ItemStack(Items.ENCHANTED_GOLDEN_APPLE, 1)); // 1 notch apple
            lootItems.add(new ItemStack(Items.EXPERIENCE_BOTTLE, 5 + random.nextInt(10))); // 5-14 XP bottles
            lootItems.add(new ItemStack(Items.ENDER_PEARL, 2 + random.nextInt(3))); // 2-4 ender pearls
            
            // Add some random enchanted books
            if (random.nextBoolean()) {
                ItemStack enchantedBook = new ItemStack(Items.ENCHANTED_BOOK);
                lootItems.add(enchantedBook);
            }
            
            // Add some food
            lootItems.add(new ItemStack(Items.GOLDEN_CARROT, 8 + random.nextInt(8))); // 8-15 golden carrots
            lootItems.add(new ItemStack(Items.COOKED_BEEF, 10 + random.nextInt(10))); // 10-19 cooked beef
            
            // Randomly place items in chest slots
            for (ItemStack item : lootItems) {
                int slot = random.nextInt(27); // Chest has 27 slots
                // Find empty slot if this one is taken
                while (!chest.getItem(slot).isEmpty() && slot < 27) {
                    slot = (slot + 1) % 27;
                }
                if (slot < 27) {
                    chest.setItem(slot, item);
                }
            }
            
            chest.setChanged();

            // Debug message to confirm loot was added
            System.out.println("Climb the Ladder: Successfully filled chest at " + chestPos + " with " + lootItems.size() + " item types");
        } else {
            // If chest entity wasn't found, log it
            System.out.println("Climb the Ladder: Failed to find chest entity at " + chestPos);
        }
    }
    
    private void cleanupStructures(MinecraftServer server) {
        for (ServerLevel level : server.getAllLevels()) {
            for (BlockPos pos : spawnedStructures) {
                if (level.isInWorldBounds(pos)) {
                    level.setBlock(pos, Blocks.AIR.defaultBlockState(), 3);
                }
            }
        }
        
        spawnedStructures.clear();
    }
}
