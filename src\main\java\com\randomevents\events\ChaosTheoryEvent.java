package com.randomevents.events;

import com.randomevents.RandomEventsMod;
import com.randomevents.manager.RandomEventManager;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

public class ChaosTheoryEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();

    
    // Events that work well together and won't conflict too much
    private static final String[] CHAOS_COMPATIBLE_EVENTS = {
        "instant_hunger", "health_swap", "blindness_blast", "slow_motion", 
        "mining_fatigue", "levitation_lift", "instant_night", "lava_rain",
        "ice_age", "desert_storm", "sky_islands", "cow_chaos", "inventory_shuffle",
        "player_swap", "leaf_bomb", "backwards_controls", "baby_zombie_swarm",
        "item_bomb", "item_rain", "slippery_world", "super_speed",
        "chicken_rain", "dance_party", "phantom_squadron"
    };
    
    @Override
    public String getId() {
        return "chaos_theory";
    }
    
    @Override
    public String getName() {
        return "§4§l§kCHAOS THEORY§r§4§l!";
    }
    
    @Override
    public String getDescription() {
        return "3 random events trigger simultaneously - pure chaos!";
    }

    @Override
    public boolean canTrigger(MinecraftServer server) {
        // Prefer to trigger on event 90 or higher, but allow after event 95 to prevent getting stuck
        com.randomevents.manager.RandomEventManager manager = com.randomevents.RandomEventsMod.getEventManager();
        if (manager != null) {
            int currentEventNumber = manager.getCurrentEventNumber();
            // Prefer event 90+, but force trigger after event 95 to prevent system getting stuck
            return currentEventNumber >= 90;
        }
        return true; // Default to true if manager not available (safety fallback)
    }
    
    @Override
    public void execute(MinecraftServer server) {
        // Show dramatic warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§4§l§kCHAOS THEORY ACTIVATED§r §c- Reality is breaking down!")));
        }
        
        // Wait 3 seconds for dramatic effect, then trigger desert storm + leaf bomb + 1 random event
        scheduler.schedule(() -> {
            RandomEventManager manager = RandomEventsMod.getEventManager();
            if (manager != null) {
                // Always trigger desert storm and leaf bomb
                List<String> selectedEvents = new ArrayList<>();
                selectedEvents.add("desert_storm");
                selectedEvents.add("leaf_bomb");

                // Add 1 random compatible event
                List<String> eventPool = new ArrayList<>();
                Collections.addAll(eventPool, CHAOS_COMPATIBLE_EVENTS);
                Collections.shuffle(eventPool);
                selectedEvents.add(eventPool.get(0));

                // Show which events are being triggered
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                        net.minecraft.network.chat.Component.literal("§c§lTriggering: §eDesert Storm §7+ §eLeaf Bomb §7+ §e" + eventPool.get(0))));
                }

                // Trigger each event with a small delay between them
                for (int i = 0; i < selectedEvents.size(); i++) {
                    final String eventId = selectedEvents.get(i);
                    scheduler.schedule(() -> {
                        manager.triggerSpecificEvent(eventId);
                    }, i * 2L, TimeUnit.SECONDS); // 2 second delay between each event
                }
            }
        }, 3L, TimeUnit.SECONDS);
    }
}
