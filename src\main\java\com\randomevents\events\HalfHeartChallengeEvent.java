package com.randomevents.events;

import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class HalfHeartChallengeEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    
    @Override
    public String getId() {
        return "half_heart_challenge";
    }
    
    @Override
    public String getName() {
        return "§4§lHalf Heart Challenge!";
    }
    
    @Override
    public String getDescription() {
        return "All players reduced to 1 heart for 30 seconds";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        // Store original health values
        java.util.Map<ServerPlayer, Float> originalHealth = new java.util.HashMap<>();
        
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            originalHealth.put(player, player.getHealth());
            player.setHealth(1.0f); // 1 heart = 2 health points, but we'll use 1 for extra challenge
        }
        
        // Restore health after 30 seconds
        scheduler.schedule(() -> {
            for (java.util.Map.Entry<ServerPlayer, Float> entry : originalHealth.entrySet()) {
                ServerPlayer player = entry.getKey();
                if (player.isAlive() && player.getServer() != null) {
                    player.setHealth(Math.min(entry.getValue(), player.getMaxHealth()));

                    // Show restoration message
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                        net.minecraft.network.chat.Component.literal("§a§lYour health has been restored!")));
                }
            }
        }, 30, TimeUnit.SECONDS);
    }
}
