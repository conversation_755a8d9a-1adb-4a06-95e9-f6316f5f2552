package com.randomevents.manager;

import com.randomevents.events.*;
import com.randomevents.network.EventCounterPacket;
import com.randomevents.network.NetworkHandler;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.ListTag;
import net.minecraft.nbt.StringTag;
import net.minecraft.network.chat.Component;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.level.saveddata.SavedData;
import net.minecraft.world.level.storage.DimensionDataStorage;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

public class RandomEventManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(RandomEventManager.class);
    private static final String DATA_NAME = "RandomEventsData";
    
    private final MinecraftServer server;
    private final List<RandomEvent> allEvents;
    private final List<RandomEvent> availableEvents;
    private final Set<String> usedEventIds;
    private final ScheduledExecutorService scheduler;

    private boolean gameActive = false;
    private ScheduledFuture<?> eventTask;
    private ScheduledFuture<?> counterTask;
    private EventData savedData;
    private int currentEventNumber = 0;
    private String currentEventName = "";

    // Event tracking (removed intensive event restrictions)
    private final Set<String> activeEvents = new HashSet<>();
    private static final int MAX_CONCURRENT_EVENTS = 5; // Increased limit since events are shorter now
    
    public RandomEventManager(MinecraftServer server) {
        this.server = server;
        this.allEvents = new ArrayList<>();
        this.availableEvents = new ArrayList<>();
        this.usedEventIds = new HashSet<>();
        this.scheduler = Executors.newSingleThreadScheduledExecutor();
        
        initializeEvents();
        loadData();
    }
    
    private void initializeEvents() {
        // Add all 69 events (26 original + 10 batch 1 + 7 batch 2 + 1 previous + 5 new - 3 deleted + 1 anvil rain + 3 new batch + 1 mob launch - 1 enderman teleport + 1 suspicious boat - 5 deleted events + 2 new events + 1 puzzle event + 1 lava geyser + 1 diamond downgrade + 1 human chain + 1 heavy snow + 1 mob magnets + 1 diamond paradise - 1 phantom squadron + 1 magma age + 1 creeper rodeo + 1 climb the ladder + 1 slot machine inventory + 1 drop held item + 1 all tools are pickaxes + 1 trust fall + 1 villager judge + 1 craft away)
        allEvents.add(new InstantHungerEvent());
        allEvents.add(new HealthSwapEvent());
        allEvents.add(new HalfHeartChallengeEvent());
        allEvents.add(new BlindnessBlastEvent());
        allEvents.add(new SlowMotionEvent());
        allEvents.add(new MiningFatigueEvent());
        allEvents.add(new LevitationLiftEvent());
        allEvents.add(new LightningStormEvent());
        allEvents.add(new InstantNightEvent());
        allEvents.add(new WeatherChaosEvent());
        allEvents.add(new LavaRainEvent());
        allEvents.add(new IceAgeEvent());
        allEvents.add(new DesertStormEvent());
        allEvents.add(new TreeExplosionEvent());
        allEvents.add(new SkyIslandsEvent());
        allEvents.add(new UndergroundOceanEvent());
        allEvents.add(new CowChaosEvent());
        allEvents.add(new InventoryBombEvent());
        allEvents.add(new RandomTeleportEvent());
        allEvents.add(new PlayerSwapEvent());
        allEvents.add(new LeafBombEvent());
        allEvents.add(new BedrockHoleEvent());
        allEvents.add(new VillagerTakeoverEvent());
        // Batch 1 events
        allEvents.add(new BackwardsControlsEvent());
        allEvents.add(new BabyZombieSwarmEvent());
        // Phantom Squadron event removed as requested
        allEvents.add(new HotPotatoBombEvent());
        allEvents.add(new ItemRainEvent());
        allEvents.add(new SuperSpeedEvent());
        allEvents.add(new ChickenRainEvent());
        allEvents.add(new DancePartyEvent());
        allEvents.add(new ChaosTheoryEvent());
        // Batch 2 events (removed quicksand_pits)
        allEvents.add(new TsunamiWaveEvent());
        allEvents.add(new TornadoEvent());
        allEvents.add(new XRayVisionEvent());
        allEvents.add(new EverythingExplodesEvent());
        allEvents.add(new SpotlightEvent());
        allEvents.add(new DemolitionDayEvent());
        // New events
        allEvents.add(new CrouchTNTEvent());
        allEvents.add(new ArrowStormOceanEvent());
        // Latest batch (5 new events)
        allEvents.add(new UltimateEvent());
        allEvents.add(new PotionSoupEvent());
        allEvents.add(new LavaOceanEvent());
        allEvents.add(new GiantFoodEvent());
        // Anvil rain event
        allEvents.add(new AnvilRainEvent());
        // New batch of 3 events (removed earthquake)
        allEvents.add(new EndermanInvasionEvent());
        allEvents.add(new SinkingWorldEvent());

        // Mob launch event
        allEvents.add(new MobLaunchEvent());

        // Hot feet and creeper cannon events
        allEvents.add(new HotFeetEvent());
        allEvents.add(new CreeperCannonEvent());
        // Color match puzzle event
        allEvents.add(new ColorMatchPuzzleEvent());
        // Lava geyser event
        allEvents.add(new LavaGeyserEvent());
        // Diamond downgrade event
        allEvents.add(new DiamondDowngradeEvent());
        // Nether invasion event
        allEvents.add(new NetherInvasionEvent());
        // Human chain event
        allEvents.add(new HumanChainEvent());
        // Heavy snow event
        allEvents.add(new HeavySnowEvent());
        // Mob magnets event
        allEvents.add(new MobMagnetsEvent());
        // Diamond paradise event
        allEvents.add(new DiamondParadiseEvent());
        // Magma age event
        allEvents.add(new MagmaAgeEvent());
        // Creeper rodeo event
        allEvents.add(new CreeperRodeoEvent());
        // Climb the ladder event
        allEvents.add(new ClimbTheLadderEvent());
        // Slot machine inventory event
        allEvents.add(new SlotMachineInventoryEvent());
        // Drop held item event
        allEvents.add(new DropHeldItemEvent());
        // All tools are pickaxes event
        allEvents.add(new AllToolsArePickaxesEvent());
        // Trust fall event
        allEvents.add(new TrustFallEvent());
        // Villager judge event
        allEvents.add(new VillagerJudgeEvent());
        // Craft away event
        allEvents.add(new CraftAwayEvent());


        LOGGER.info("Initialized {} random events", allEvents.size());
    }
    
    private void loadData() {
        DimensionDataStorage storage = server.overworld().getDataStorage();
        savedData = storage.computeIfAbsent(EventData::load, EventData::new, DATA_NAME);
        
        usedEventIds.clear();
        usedEventIds.addAll(savedData.getUsedEvents());
        
        refreshAvailableEvents();
        LOGGER.info("Loaded event data. Used events: {}, Available events: {}", 
                   usedEventIds.size(), availableEvents.size());
    }
    
    private void refreshAvailableEvents() {
        availableEvents.clear();
        for (RandomEvent event : allEvents) {
            if (!usedEventIds.contains(event.getId())) {
                availableEvents.add(event);
            }
        }
        Collections.shuffle(availableEvents);
    }
    
    public boolean startEvents() {
        if (gameActive) {
            return false;
        }
        
        if (availableEvents.isEmpty()) {
            broadcastMessage("§cNo events available! Use /randomevents reset to reset the event pool.");
            return false;
        }
        
        gameActive = true;
        currentEventNumber = 0;
        currentEventName = "§7Waiting for first event...";
        startCounterDisplay();
        scheduleNextEvent();
        broadcastMessage("§a§lRandom Events Started!");
        LOGGER.info("Random events started. {} events available.", availableEvents.size());
        return true;
    }
    
    public boolean stopEvents() {
        if (!gameActive) {
            return false;
        }
        
        gameActive = false;
        if (eventTask != null) {
            eventTask.cancel(false);
            eventTask = null;
        }
        if (counterTask != null) {
            counterTask.cancel(false);
            counterTask = null;
        }

        // Clear active events tracking
        activeEvents.clear();

        // Clear the counter display
        EventCounterPacket hidePacket = new EventCounterPacket("", false);
        NetworkHandler.sendToAllPlayers(hidePacket);

        broadcastMessage("§c§lRandom Events Stopped!");
        LOGGER.info("Random events stopped.");
        return true;
    }
    
    public void resetEvents() {
        stopEvents();
        usedEventIds.clear();
        savedData.clearUsedEvents();
        savedData.setDirty();
        refreshAvailableEvents();

        // Reset counter
        currentEventNumber = 0;
        currentEventName = "";

        broadcastMessage("§e§lEvent pool reset! §rAll " + allEvents.size() + " events are now available.");
        LOGGER.info("Event pool reset. All {} events available.", allEvents.size());
    }
    
    private void scheduleNextEvent() {
        if (!gameActive) return;

        eventTask = scheduler.schedule(() -> {
            if (gameActive) {
                triggerRandomEvent();
                // Always schedule the next event, regardless of whether current event triggered
                scheduleNextEvent();
            }
        }, 30, TimeUnit.SECONDS);
    }
    
    private void triggerRandomEvent() {
        if (availableEvents.isEmpty()) {
            broadcastMessage("§6§lAll events completed! §rUse /randomevents reset to play again.");
            stopEvents();
            return;
        }

        // Simplified event limiting - just check total concurrent events

        // Special case: Prioritize chicken_rain for event #4
        if (currentEventNumber == 3) { // Next event will be #4
            for (int i = 0; i < availableEvents.size(); i++) {
                RandomEvent candidateEvent = availableEvents.get(i);
                if ("chicken_rain".equals(candidateEvent.getId())) {
                    // Move chicken_rain to the front for event #4
                    availableEvents.remove(i);
                    availableEvents.add(0, candidateEvent);
                    LOGGER.info("Prioritized chicken_rain for event #4");
                    break;
                }
            }
        }

        // Find an event that can trigger under current conditions
        RandomEvent event = null;
        int attempts = 0;
        while (event == null && attempts < availableEvents.size()) {
            RandomEvent candidateEvent = availableEvents.get(0);

            // Check if this event would exceed our limits (simplified - just check total events)
            boolean wouldExceedLimit = activeEvents.size() >= MAX_CONCURRENT_EVENTS;
            boolean canTrigger = candidateEvent.canTrigger(server);

            LOGGER.debug("Checking event: {} (attempt {}), canTrigger: {}, wouldExceedLimit: {}, currentEventNumber: {}, activeEvents: {}",
                candidateEvent.getId(), attempts + 1, canTrigger, wouldExceedLimit, currentEventNumber, activeEvents.size());

            if (canTrigger && !wouldExceedLimit) {
                event = candidateEvent;
                availableEvents.remove(0);
                LOGGER.info("Selected event: {} for event #{}", event.getId(), currentEventNumber + 1);
            } else {
                // Move this event to the back of the list and try the next one
                availableEvents.remove(0);
                availableEvents.add(candidateEvent);
                attempts++;
                LOGGER.debug("Skipped event: {} (canTrigger: {}, wouldExceedLimit: {})", candidateEvent.getId(), canTrigger, wouldExceedLimit);
            }
        }

        // If no event can trigger, skip this cycle
        if (event == null) {
            if (activeEvents.size() >= MAX_CONCURRENT_EVENTS) {
                LOGGER.warn("Too many events running ({}), skipping cycle. Active events: {}", activeEvents.size(), activeEvents);
            } else {
                LOGGER.warn("No events can trigger under current conditions, skipping cycle. Available events: {}, attempts: {}", availableEvents.size(), attempts);
                LOGGER.warn("Current event number: {}, Active events: {}", currentEventNumber, activeEvents);

                // Enhanced safety mechanism: If we've tried all events and none can trigger,
                // force trigger the first available event to prevent getting stuck
                if (attempts >= availableEvents.size() && !availableEvents.isEmpty()) {
                    LOGGER.warn("Forcing trigger of first available event to prevent system getting stuck");
                    event = availableEvents.get(0);
                    availableEvents.remove(0);
                } else {
                    LOGGER.warn("Emergency fallback: Clearing active events and forcing trigger to prevent permanent stuck state");
                    // Emergency: Clear all active events and force trigger
                    activeEvents.clear();
                    if (!availableEvents.isEmpty()) {
                        event = availableEvents.get(0);
                        availableEvents.remove(0);
                        LOGGER.warn("Emergency: Cleared active events and forcing event: {}", event.getId());
                    } else {
                        LOGGER.error("No available events left! System is completely stuck.");
                        return;
                    }
                }
            }
        }

        usedEventIds.add(event.getId());
        savedData.addUsedEvent(event.getId());
        savedData.setDirty();

        // Add to active events tracking
        activeEvents.add(event.getId());

        // Update counter
        currentEventNumber++;
        currentEventName = event.getName();

        // Update counter display immediately when event triggers
        updateCounterDisplay();

        broadcastMessage("§c§l[RANDOM EVENT] §r" + event.getName());

        try {
            event.execute(server);
            LOGGER.info("Executed event: {} (ID: {})", event.getName(), event.getId());

            // Create final copy for lambda
            final String eventId = event.getId();

            // Schedule removal from active events after event duration
            scheduler.schedule(() -> {
                activeEvents.remove(eventId);
                LOGGER.debug("Event {} finished, removed from active events", eventId);
            }, getEventDuration(eventId), TimeUnit.SECONDS);

        } catch (Exception e) {
            LOGGER.error("Error executing event: {} (ID: {})", event.getName(), event.getId(), e);
            broadcastMessage("§cError executing event: " + event.getName());
            // Remove from active events if execution failed
            activeEvents.remove(event.getId());
        }
    }
    
    private void broadcastMessage(String message) {
        Component component = Component.literal(message);
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.sendSystemMessage(component);
        }
    }
    
    public boolean isGameActive() {
        return gameActive;
    }
    
    public int getAvailableEventCount() {
        return availableEvents.size();
    }
    
    public int getTotalEventCount() {
        return allEvents.size();
    }
    
    public int getUsedEventCount() {
        return usedEventIds.size();
    }

    public int getCurrentEventNumber() {
        return currentEventNumber;
    }

    public String getCurrentEventName() {
        return currentEventName;
    }

    public void setCurrentEventNumber(int eventNumber) {
        this.currentEventNumber = eventNumber;

        // Update the counter display immediately
        updateCounterDisplay();

        // Save the new event number
        if (savedData != null) {
            savedData.setDirty();
        }

        LOGGER.info("Event number manually set to: {}", eventNumber);
    }

    private long getEventDuration(String eventId) {
        // Return estimated duration for each event type (in seconds)
        switch (eventId) {
            case "anvil_rain":
            case "lava_rain":
            case "item_rain":
            case "chicken_rain":
                return 45L; // Rain events typically last 30-45 seconds
            case "tsunami_wave":
                return 30L; // Tsunami wave lasts 30 seconds
            case "sinking_world":
            case "lava_ocean":
            case "underground_ocean":
                return 120L; // Ocean/flooding events last 2 minutes
            case "desert_storm":
            case "tornado":
                return 60L; // Weather events last 1 minute
            case "everything_explodes":
            case "demolition_day":
            case "tree_explosion":
                return 30L; // Explosion events are quick
            case "arrow_storm_ocean":
            case "mob_launch":
            case "enderman_invasion":
                return 90L; // Combat events last 1.5 minutes
            case "lava_geysers":
                return 75L; // Geysers last 75 seconds
            case "nether_invasion":
                return 180L; // Nether invasion lasts 3 minutes (includes cleanup)
            case "trust_fall":
                return 120L; // Trust fall ceremony lasts 2 minutes
            case "chaos_theory":
            case "ultimate_event":
                return 150L; // Ultimate events last 2.5 minutes
            case "creeper_rodeo":
                return 15L; // Creeper rodeo lasts 15 seconds (3s setup + 10s rodeo + 2s cleanup)
            case "climb_the_ladder":
                return 300L; // Climb the ladder lasts 5 minutes (structures stay up for exploration)
            case "all_tools_are_pickaxes":
                return 30L; // All tools are pickaxes lasts 30 seconds
            case "ice_age":
                return 30L; // Ice age lasts 30 seconds
            case "heavy_snow":
                return 35L; // Heavy snow lasts 35 seconds
            case "half_heart_challenge":
                return 30L; // Half heart challenge lasts 30 seconds
            case "item_bomb":
                return 5L; // Item bomb is instant, just 5 seconds for cleanup
            default:
                return 60L; // Default 1 minute for other events
        }
    }

    public boolean triggerSpecificEvent(String eventId) {
        // Find the event by ID
        RandomEvent eventToTrigger = null;
        for (RandomEvent event : allEvents) {
            if (event.getId().equals(eventId)) {
                eventToTrigger = event;
                break;
            }
        }

        if (eventToTrigger == null) {
            return false; // Event not found
        }

        // Trigger the event WITHOUT chat message (silent execution)
        try {
            eventToTrigger.execute(server);
            LOGGER.info("Manually executed event: {} (ID: {})", eventToTrigger.getName(), eventToTrigger.getId());
            return true;
        } catch (Exception e) {
            LOGGER.error("Error manually executing event: {} (ID: {})", eventToTrigger.getName(), eventToTrigger.getId(), e);
            return false;
        }
    }

    public String debugClearActiveEvents() {
        int clearedCount = activeEvents.size();
        activeEvents.clear();
        LOGGER.info("Debug: Cleared {} active events", clearedCount);
        return "§e§lDEBUG: Cleared " + clearedCount + " active events. Next event will trigger in 30 seconds.";
    }

    public String debugForceNextEvent() {
        if (!gameActive) {
            return "§c§lDEBUG: Events are not currently running!";
        }

        String clearMessage = debugClearActiveEvents();

        // Cancel current scheduled event and trigger next one immediately
        if (eventTask != null) {
            eventTask.cancel(false);
            LOGGER.info("Debug: Cancelled existing event task");
        }

        // Trigger the next event immediately and continue the normal scheduling
        eventTask = scheduler.schedule(() -> {
            if (gameActive) {
                LOGGER.info("Debug: Executing forced event trigger");
                triggerRandomEvent();
                LOGGER.info("Debug: Scheduling next event to continue the loop");
                scheduleNextEvent(); // This ensures the event loop continues
            } else {
                LOGGER.warn("Debug: gameActive is false, not triggering event");
            }
        }, 2, TimeUnit.SECONDS); // Reduced to 2 seconds for faster debug

        LOGGER.info("Debug: Forced next event scheduling with continued loop. gameActive: {}, availableEvents: {}", gameActive, availableEvents.size());
        return "§a§lDEBUG: Forcing next event in 2 seconds... Events will continue normally after.";
    }

    private void startCounterDisplay() {
        // Update the counter display every 2 seconds
        counterTask = scheduler.scheduleAtFixedRate(() -> {
            if (gameActive) {
                updateCounterDisplay();
            }
        }, 0, 2, TimeUnit.SECONDS);
    }

    private void updateCounterDisplay() {
        String counterText;
        if (currentEventNumber == 0) {
            counterText = "Event Counter: Waiting for first event...";
        } else {
            int totalEvents = allEvents.size();
            int eventsCompleted = usedEventIds.size();
            // Clean text without color codes (we'll handle colors in the renderer)
            counterText = String.format("Event #%d - %s (%d/%d completed)",
                currentEventNumber, stripColorCodes(currentEventName), eventsCompleted, totalEvents);
        }

        // Send custom packet to all players for client-side rendering
        EventCounterPacket packet = new EventCounterPacket(counterText, true);
        NetworkHandler.sendToAllPlayers(packet);
    }

    private String stripColorCodes(String text) {
        // Remove Minecraft color codes for cleaner display
        return text.replaceAll("§[0-9a-fk-or]", "");
    }

    public void shutdown() {
        stopEvents();
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
    
    // Saved data class for persistence
    public static class EventData extends SavedData {
        private final Set<String> usedEvents = new HashSet<>();
        
        public EventData() {}
        
        public static EventData load(CompoundTag tag) {
            EventData data = new EventData();
            ListTag usedList = tag.getList("UsedEvents", 8); // 8 = String tag type
            for (int i = 0; i < usedList.size(); i++) {
                data.usedEvents.add(usedList.getString(i));
            }
            return data;
        }
        
        @Override
        public CompoundTag save(CompoundTag tag) {
            ListTag usedList = new ListTag();
            for (String eventId : usedEvents) {
                usedList.add(StringTag.valueOf(eventId));
            }
            tag.put("UsedEvents", usedList);
            return tag;
        }
        
        public Set<String> getUsedEvents() {
            return new HashSet<>(usedEvents);
        }
        
        public void addUsedEvent(String eventId) {
            usedEvents.add(eventId);
        }
        
        public void clearUsedEvents() {
            usedEvents.clear();
        }
    }
}

