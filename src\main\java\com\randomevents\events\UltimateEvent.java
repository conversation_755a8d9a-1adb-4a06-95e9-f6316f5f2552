package com.randomevents.events;

import com.randomevents.RandomEventsMod;
import com.randomevents.manager.RandomEventManager;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class UltimateEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    
    // Ultimate Event now triggers 5 specific events for maximum chaos
    
    @Override
    public String getId() {
        return "ultimate_event";
    }
    
    @Override
    public String getName() {
        return "§4§l§kTHE ULTIMATE EVENT§r§4§l!";
    }
    
    @Override
    public String getDescription() {
        return "Combines 5 random events simultaneously for MAXIMUM CHAOS!";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        // Show dramatic warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§4§l§kTHE ULTIMATE EVENT§r §c- PREPARE FOR ABSOLUTE CHAOS!")));
        }
        
        // Wait 5 seconds for dramatic effect, then trigger 5 random events
        scheduler.schedule(() -> {
            RandomEventManager manager = RandomEventsMod.getEventManager();
            if (manager != null) {
                // Fixed set of 5 specific events for maximum chaos
                List<String> selectedEvents = Arrays.asList(
                    "sky_islands",
                    "lava_rain",
                    "item_rain",
                    "instant_hunger",
                    "giant_food"
                );

                // Show which events are being triggered
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                        net.minecraft.network.chat.Component.literal("§4§lULTIMATE CHAOS: §esky_islands §7+ §elava_rain §7+ §eitem_rain §7+ §einstant_hunger §7+ §egiant_food")));
                }
                
                // Trigger each event with a small delay between them
                for (int i = 0; i < selectedEvents.size(); i++) {
                    final String eventId = selectedEvents.get(i);
                    final int eventNumber = i + 1;
                    scheduler.schedule(() -> {
                        // Show countdown
                        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                                net.minecraft.network.chat.Component.literal("§4§lActivating Event " + eventNumber + "/5: §e" + eventId)));
                        }
                        manager.triggerSpecificEvent(eventId);
                    }, i * 3L, TimeUnit.SECONDS); // 3 second delay between each event
                }
                
                // Final message
                scheduler.schedule(() -> {
                    for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                        player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                            net.minecraft.network.chat.Component.literal("§4§l§kULTIMATE CHAOS UNLEASHED!§r §cSurvive if you can!")));
                    }
                }, 15L, TimeUnit.SECONDS);
            }
        }, 5L, TimeUnit.SECONDS);
    }
}
