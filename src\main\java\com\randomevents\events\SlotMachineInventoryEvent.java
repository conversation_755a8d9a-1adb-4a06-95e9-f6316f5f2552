package com.randomevents.events;

import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.SimpleMenuProvider;
import net.minecraft.world.inventory.ChestMenu;
import net.minecraft.world.inventory.MenuType;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.SimpleContainer;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class SlotMachineInventoryEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    private static final Map<ServerPlayer, Boolean> animatingPlayers = new HashMap<>();
    
    // Pool of items for the slot machine (simplified to 3 high-value items)
    private static final ItemStack[] SLOT_ITEMS = {
        new ItemStack(Items.TOTEM_OF_UNDYING, 1),           // Best prize
        new ItemStack(Items.ENCHANTED_GOLDEN_APPLE, 3),     // Good prize
        new ItemStack(Items.DIRT, 1)                        // Worst prize
    };
    
    @Override
    public String getId() {
        return "slot_machine_inventory";
    }
    
    @Override
    public String getName() {
        return "§6§lSlot Machine Inventory!";
    }
    
    @Override
    public String getDescription() {
        return "3 random items scroll like a slot machine and you get one";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            startSlotMachine(player);
        }
    }

    private void startSlotMachine(ServerPlayer player) {
        // Mark player as animating
        animatingPlayers.put(player, true);

        // Create a completely locked container that prevents all item interactions
        SimpleContainer container = new SimpleContainer(9) {
            @Override
            public boolean canPlaceItem(int index, ItemStack stack) {
                return false; // Never allow placing items
            }

            @Override
            public ItemStack removeItem(int index, int count) {
                // Only allow removal if animation is finished
                if (animatingPlayers.getOrDefault(player, false)) {
                    return ItemStack.EMPTY;
                }
                return super.removeItem(index, count);
            }

            @Override
            public ItemStack removeItemNoUpdate(int index) {
                // Only allow removal if animation is finished
                if (animatingPlayers.getOrDefault(player, false)) {
                    return ItemStack.EMPTY;
                }
                return super.removeItemNoUpdate(index);
            }

            @Override
            public void setItem(int index, ItemStack stack) {
                // Only allow setting items during animation (for the slot machine effect)
                super.setItem(index, stack);
            }
        };

        // Create a custom chest menu that prevents item interactions
        ChestMenu customMenu = new ChestMenu(MenuType.GENERIC_9x1, 0, player.getInventory(), container, 1) {
            @Override
            public ItemStack quickMoveStack(net.minecraft.world.entity.player.Player player, int index) {
                // Prevent shift-clicking items during animation
                if (animatingPlayers.getOrDefault((ServerPlayer) player, false)) {
                    return ItemStack.EMPTY;
                }
                return ItemStack.EMPTY; // Never allow quick move
            }

            @Override
            public boolean stillValid(net.minecraft.world.entity.player.Player player) {
                return true; // Keep menu open
            }

            @Override
            public void clicked(int slotId, int button, net.minecraft.world.inventory.ClickType clickType, net.minecraft.world.entity.player.Player player) {
                // Prevent all clicking during animation
                if (animatingPlayers.getOrDefault((ServerPlayer) player, false)) {
                    return; // Block all clicks
                }
                super.clicked(slotId, button, clickType, player);
            }
        };

        // Open the custom menu
        player.openMenu(new SimpleMenuProvider(
            (id, inventory, p) -> customMenu,
            net.minecraft.network.chat.Component.literal("§6§lSlot Machine")
        ));

        // Start the spinning animation
        animateSlotMachine(player, container);
    }

    private void animateSlotMachine(ServerPlayer player, SimpleContainer container) {
        // Animate for 3 seconds (15 changes, every 0.2 seconds)
        for (int i = 0; i < 15; i++) {
            scheduler.schedule(() -> {
                // Pick 3 random items for slots 3, 4, 5 (center of the 9-slot container)
                ItemStack item1 = SLOT_ITEMS[random.nextInt(SLOT_ITEMS.length)].copy();
                ItemStack item2 = SLOT_ITEMS[random.nextInt(SLOT_ITEMS.length)].copy();
                ItemStack item3 = SLOT_ITEMS[random.nextInt(SLOT_ITEMS.length)].copy();

                // Clear container and set the 3 items in the center
                container.clearContent();
                container.setItem(3, item1); // Left slot
                container.setItem(4, item2); // Center slot
                container.setItem(5, item3); // Right slot

                // Add decorative borders (glass panes)
                container.setItem(0, new ItemStack(Items.BLACK_STAINED_GLASS_PANE));
                container.setItem(1, new ItemStack(Items.BLACK_STAINED_GLASS_PANE));
                container.setItem(2, new ItemStack(Items.BLACK_STAINED_GLASS_PANE));
                container.setItem(6, new ItemStack(Items.BLACK_STAINED_GLASS_PANE));
                container.setItem(7, new ItemStack(Items.BLACK_STAINED_GLASS_PANE));
                container.setItem(8, new ItemStack(Items.BLACK_STAINED_GLASS_PANE));

            }, i * 200L, TimeUnit.MILLISECONDS); // Every 0.2 seconds
        }

        // After 3 seconds, stop and give the final item
        scheduler.schedule(() -> {
            // Pick the final winning item
            ItemStack winningItem = SLOT_ITEMS[random.nextInt(SLOT_ITEMS.length)].copy();

            // Clear container and show only the winning item in center
            container.clearContent();
            container.setItem(4, winningItem); // Center slot

            // Add golden borders to show winning
            for (int i = 0; i < 9; i++) {
                if (i != 4) { // Don't overwrite the winning item
                    container.setItem(i, new ItemStack(Items.YELLOW_STAINED_GLASS_PANE));
                }
            }

            // Give the item to the player
            player.getInventory().add(winningItem);

            // Animation is finished - allow item interactions again
            animatingPlayers.put(player, false);

            // Close the GUI after 2 seconds
            scheduler.schedule(() -> {
                player.closeContainer();
                // Clean up tracking
                animatingPlayers.remove(player);
            }, 2L, TimeUnit.SECONDS);

        }, 3L, TimeUnit.SECONDS);
    }
}
