package com.randomevents.events;

import net.minecraft.network.chat.Component;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.SimpleMenuProvider;
import net.minecraft.world.inventory.ChestMenu;
import net.minecraft.world.inventory.MenuType;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.SimpleContainer;

import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class SlotMachineInventoryEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    
    // Pool of items for the slot machine
    private static final ItemStack[] SLOT_ITEMS = {
        new ItemStack(Items.DIAMOND, 1),
        new ItemStack(Items.EMERALD, 1),
        new ItemStack(Items.GOLD_INGOT, 1),
        new ItemStack(Items.IRON_INGOT, 1),
        new ItemStack(Items.COAL, 1),
        new ItemStack(Items.REDSTONE, 1),
        new ItemStack(Items.LAPIS_LAZULI, 1),
        new ItemStack(Items.BREAD, 1),
        new ItemStack(Items.APPLE, 1),
        new ItemStack(Items.STICK, 1),
        new ItemStack(Items.COBBLESTONE, 1),
        new ItemStack(Items.DIRT, 1),
        new ItemStack(Items.STONE, 1),
        new ItemStack(Items.OAK_LOG, 1),
        new ItemStack(Items.SAND, 1)
    };
    
    @Override
    public String getId() {
        return "slot_machine_inventory";
    }
    
    @Override
    public String getName() {
        return "§6§lSlot Machine Inventory!";
    }
    
    @Override
    public String getDescription() {
        return "3 random items scroll like a slot machine and you get one";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            startSlotMachine(player);
        }
    }

    private void startSlotMachine(ServerPlayer player) {
        // Create a simple container for the slot machine UI
        SimpleContainer container = new SimpleContainer(9); // 9 slots (3x3 grid)

        // Open the container as a chest GUI
        player.openMenu(new SimpleMenuProvider(
            (id, inventory, p) -> new ChestMenu(MenuType.GENERIC_9x1, id, inventory, container, 1),
            net.minecraft.network.chat.Component.literal("§6§lSlot Machine")
        ));

        // Start the spinning animation
        animateSlotMachine(player, container);
    }

    private void animateSlotMachine(ServerPlayer player, SimpleContainer container) {
        // Animate for 3 seconds (15 changes, every 0.2 seconds)
        for (int i = 0; i < 15; i++) {
            scheduler.schedule(() -> {
                // Pick 3 random items for slots 3, 4, 5 (center of the 9-slot container)
                ItemStack item1 = SLOT_ITEMS[random.nextInt(SLOT_ITEMS.length)].copy();
                ItemStack item2 = SLOT_ITEMS[random.nextInt(SLOT_ITEMS.length)].copy();
                ItemStack item3 = SLOT_ITEMS[random.nextInt(SLOT_ITEMS.length)].copy();

                // Clear container and set the 3 items in the center
                container.clearContent();
                container.setItem(3, item1); // Left slot
                container.setItem(4, item2); // Center slot
                container.setItem(5, item3); // Right slot

                // Add decorative borders (glass panes)
                container.setItem(0, new ItemStack(Items.BLACK_STAINED_GLASS_PANE));
                container.setItem(1, new ItemStack(Items.BLACK_STAINED_GLASS_PANE));
                container.setItem(2, new ItemStack(Items.BLACK_STAINED_GLASS_PANE));
                container.setItem(6, new ItemStack(Items.BLACK_STAINED_GLASS_PANE));
                container.setItem(7, new ItemStack(Items.BLACK_STAINED_GLASS_PANE));
                container.setItem(8, new ItemStack(Items.BLACK_STAINED_GLASS_PANE));

            }, i * 200L, TimeUnit.MILLISECONDS); // Every 0.2 seconds
        }

        // After 3 seconds, stop and give the final item
        scheduler.schedule(() -> {
            // Pick the final winning item
            ItemStack winningItem = SLOT_ITEMS[random.nextInt(SLOT_ITEMS.length)].copy();

            // Clear container and show only the winning item in center
            container.clearContent();
            container.setItem(4, winningItem); // Center slot

            // Add golden borders to show winning
            for (int i = 0; i < 9; i++) {
                if (i != 4) { // Don't overwrite the winning item
                    container.setItem(i, new ItemStack(Items.YELLOW_STAINED_GLASS_PANE));
                }
            }

            // Give the item to the player
            player.getInventory().add(winningItem);

            // Close the GUI after 2 seconds
            scheduler.schedule(() -> {
                player.closeContainer();
            }, 2L, TimeUnit.SECONDS);

        }, 3L, TimeUnit.SECONDS);
    }
}
