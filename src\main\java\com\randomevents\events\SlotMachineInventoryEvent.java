package com.randomevents.events;

import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;

import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class SlotMachineInventoryEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    
    // Pool of items for the slot machine
    private static final ItemStack[] SLOT_ITEMS = {
        new ItemStack(Items.DIAMOND, 1),
        new ItemStack(Items.EMERALD, 1),
        new ItemStack(Items.GOLD_INGOT, 1),
        new ItemStack(Items.IRON_INGOT, 1),
        new ItemStack(Items.COAL, 1),
        new ItemStack(Items.REDSTONE, 1),
        new ItemStack(Items.LAPIS_LAZULI, 1),
        new ItemStack(Items.BREAD, 1),
        new ItemStack(Items.APPLE, 1),
        new ItemStack(Items.STICK, 1),
        new ItemStack(Items.COBBLESTONE, 1),
        new ItemStack(Items.DIRT, 1),
        new ItemStack(Items.STONE, 1),
        new ItemStack(Items.OAK_LOG, 1),
        new ItemStack(Items.SAND, 1)
    };
    
    @Override
    public String getId() {
        return "slot_machine_inventory";
    }
    
    @Override
    public String getName() {
        return "§6§lSlot Machine Inventory!";
    }
    
    @Override
    public String getDescription() {
        return "3 random items scroll like a slot machine and you get one";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            startSlotMachine(player);
        }
    }
    
    private void startSlotMachine(ServerPlayer player) {
        // Show initial message
        player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
            net.minecraft.network.chat.Component.literal("§6§lSLOT MACHINE SPINNING! §7Watch the items scroll...")));
        
        // Scroll through items for 3 seconds (15 changes, every 0.2 seconds)
        for (int i = 0; i < 15; i++) {
            scheduler.schedule(() -> {
                // Pick 3 random items
                ItemStack item1 = SLOT_ITEMS[random.nextInt(SLOT_ITEMS.length)].copy();
                ItemStack item2 = SLOT_ITEMS[random.nextInt(SLOT_ITEMS.length)].copy();
                ItemStack item3 = SLOT_ITEMS[random.nextInt(SLOT_ITEMS.length)].copy();
                
                // Show the 3 items scrolling
                String item1Name = item1.getDisplayName().getString();
                String item2Name = item2.getDisplayName().getString();
                String item3Name = item3.getDisplayName().getString();
                
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§6§l[ " + item1Name + " ] [ " + item2Name + " ] [ " + item3Name + " ]")));
                
            }, i * 200L, TimeUnit.MILLISECONDS); // Every 0.2 seconds
        }
        
        // After 3 seconds, stop and give the final item
        scheduler.schedule(() -> {
            // Pick the final winning item
            ItemStack winningItem = SLOT_ITEMS[random.nextInt(SLOT_ITEMS.length)].copy();
            
            // Show the final result
            String winningName = winningItem.getDisplayName().getString();
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§a§lWINNER: " + winningName + "! §7Added to inventory!")));
            
            // Give the item to the player
            player.getInventory().add(winningItem);
            
        }, 3L, TimeUnit.SECONDS);
    }
}
