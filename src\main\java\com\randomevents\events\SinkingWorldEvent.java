package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class SinkingWorldEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    private static final Map<BlockPos, BlockState> originalBlocks = new HashMap<>();
    
    @Override
    public String getId() {
        return "sinking_world";
    }
    
    @Override
    public String getName() {
        return "§8§lSinking World!";
    }
    
    @Override
    public String getDescription() {
        return "The entire ground level slowly drops by 10 blocks over 2 minutes";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        originalBlocks.clear();
        
        // Show warning message for 30 seconds only
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§8§lThe world is sinking...")));
        }

        // Clear message after 30 seconds
        scheduler.schedule(() -> {
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("")));
            }
        }, 30L, TimeUnit.SECONDS);
        
        // Wait 3 seconds then start the sinking process
        scheduler.schedule(() -> {
            // Sink the world in 10 stages over 2 minutes (every 12 seconds)
            for (int stage = 1; stage <= 10; stage++) {
                final int currentStage = stage;
                scheduler.schedule(() -> {
                    for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                        ServerLevel level = player.serverLevel();
                        BlockPos playerPos = player.blockPosition();
                        
                        // Define the area to sink around each player (80x80 area)
                        int radius = 40;
                        
                        // No stage messages
                        
                        // Sink one layer at a time
                        for (int x = playerPos.getX() - radius; x <= playerPos.getX() + radius; x++) {
                            for (int z = playerPos.getZ() - radius; z <= playerPos.getZ() + radius; z++) {
                                // Get the current surface level
                                int surfaceY = level.getHeight(net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING, x, z);
                                
                                // The layer we want to remove (starts from surface and goes down)
                                int targetY = surfaceY - currentStage + 1;
                                
                                BlockPos targetPos = new BlockPos(x, targetY, z);
                                
                                if (level.isInWorldBounds(targetPos)) {
                                    BlockState currentState = level.getBlockState(targetPos);
                                    
                                    // Don't remove air, bedrock, or important blocks
                                    if (!currentState.isAir() && 
                                        !currentState.is(Blocks.BEDROCK) &&
                                        !currentState.is(Blocks.WATER) &&
                                        !currentState.is(Blocks.LAVA)) {
                                        
                                        // Store original block for potential restoration
                                        if (currentStage == 1) { // Only store on first stage
                                            originalBlocks.put(targetPos, currentState);
                                        }
                                        
                                        // Remove the block (make it air) with reduced tick updates
                                        level.setBlock(targetPos, Blocks.AIR.defaultBlockState(), 2);
                                        
                                        // Occasionally play collapse sounds
                                        if (random.nextInt(100) == 0) { // 1% chance
                                            level.playSound(null, targetPos, SoundEvents.GRAVEL_BREAK, SoundSource.BLOCKS, 0.3f, 0.5f + random.nextFloat() * 0.5f);
                                        }
                                    }
                                }
                            }
                        }
                        
                        // Move players down if they're standing on air after sinking
                        BlockPos belowPlayer = playerPos.below();
                        if (level.getBlockState(belowPlayer).isAir()) {
                            // Find the new ground level
                            int newGroundY = level.getHeight(net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING, playerPos.getX(), playerPos.getZ());
                            if (newGroundY < playerPos.getY() - 2) {
                                player.teleportTo(level, playerPos.getX() + 0.5, newGroundY + 1, playerPos.getZ() + 0.5, player.getYRot(), player.getXRot());
                            }
                        }
                    }
                    
                    // No dramatic messages during sinking
                }, (currentStage - 1) * 12L, TimeUnit.SECONDS); // Every 12 seconds
            }
            
            // After sinking is complete (2 minutes), start restoration process
            scheduler.schedule(() -> {
                // No completion message
                
                // Clear the stored blocks (no restoration - damage is permanent)
                originalBlocks.clear();
                
            }, 120L, TimeUnit.SECONDS); // Start restoration after 2 minutes
            
        }, 3L, TimeUnit.SECONDS); // 3 second delay after warning
    }
}
