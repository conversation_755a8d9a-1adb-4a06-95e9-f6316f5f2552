package com.randomevents.events;

import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;

import java.util.Random;

public class HotPotatoBombEvent extends RandomEvent {
    private static final Random random = new Random();
    
    @Override
    public String getId() {
        return "item_bomb";
    }

    @Override
    public String getName() {
        return "§c§lItem Bomb!";
    }

    @Override
    public String getDescription() {
        return "One random item from each player's inventory explodes";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            // Find all non-empty slots with non-important items
            java.util.List<Integer> explodableSlots = new java.util.ArrayList<>();
            for (int slot = 0; slot < 36; slot++) { // Main inventory slots
                ItemStack item = player.getInventory().getItem(slot);
                if (!item.isEmpty() && !shouldProtectItem(item)) {
                    explodableSlots.add(slot);
                }
            }

            // If player has explodable items, explode one randomly
            if (!explodableSlots.isEmpty()) {
                int randomSlot = explodableSlots.get(random.nextInt(explodableSlots.size()));
                ItemStack explodingItem = player.getInventory().getItem(randomSlot);

                // Show which item is exploding
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§c§l💥 " + explodingItem.getDisplayName().getString() + " exploded!")));

                // Remove the item
                player.getInventory().setItem(randomSlot, ItemStack.EMPTY);

                // Create explosion effect at player location (visual only)
                player.serverLevel().explode(null, player.getX(), player.getY(), player.getZ(),
                                           0.0f, false, net.minecraft.world.level.Level.ExplosionInteraction.NONE);
            } else {
                // No explodable items found
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§a§lYour important items are protected!")));
            }
        }
    }

    private boolean shouldProtectItem(ItemStack item) {
        if (item.isEmpty()) {
            return false; // Empty slots can be exploded
        }

        // Protect important items
        return item.is(Items.TOTEM_OF_UNDYING) ||           // Totems
               item.is(Items.ELYTRA) ||                     // Elytra
               item.is(Items.NETHERITE_HELMET) ||           // Netherite armor
               item.is(Items.NETHERITE_CHESTPLATE) ||
               item.is(Items.NETHERITE_LEGGINGS) ||
               item.is(Items.NETHERITE_BOOTS) ||
               item.is(Items.DIAMOND_HELMET) ||             // Diamond armor
               item.is(Items.DIAMOND_CHESTPLATE) ||
               item.is(Items.DIAMOND_LEGGINGS) ||
               item.is(Items.DIAMOND_BOOTS) ||
               item.is(Items.NETHERITE_SWORD) ||            // Netherite tools/weapons
               item.is(Items.NETHERITE_PICKAXE) ||
               item.is(Items.NETHERITE_AXE) ||
               item.is(Items.NETHERITE_SHOVEL) ||
               item.is(Items.NETHERITE_HOE) ||
               item.is(Items.DIAMOND_SWORD) ||              // Diamond tools/weapons
               item.is(Items.DIAMOND_PICKAXE) ||
               item.is(Items.DIAMOND_AXE) ||
               item.is(Items.DIAMOND_SHOVEL) ||
               item.is(Items.DIAMOND_HOE) ||
               item.is(Items.ENCHANTED_GOLDEN_APPLE) ||     // God apples
               item.is(Items.GOLDEN_APPLE) ||               // Golden apples
               item.is(Items.ENDER_PEARL) ||                // Ender pearls
               item.is(Items.CHORUS_FRUIT) ||               // Chorus fruit
               item.getEnchantmentTags().size() > 0;        // Any enchanted item
    }
}
