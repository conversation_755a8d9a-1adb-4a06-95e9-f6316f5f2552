package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;

import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class HeavySnowEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    
    @Override
    public String getId() {
        return "heavy_snow";
    }
    
    @Override
    public String getName() {
        return "§f§lHeavy Snow!";
    }
    
    @Override
    public String getDescription() {
        return "Heavy snow falls from the sky for 35 seconds, then clears to day time";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§f§lThe sky is getting dark... §7A blizzard is coming!")));
        }

        // Wait 3 seconds then start the heavy snow weather
        scheduler.schedule(() -> {
            // Set heavy snow weather for all overworld levels (pure snow, no rain)
            for (ServerLevel level : server.getAllLevels()) {
                if (level.dimension() == ServerLevel.OVERWORLD) {
                    // Set snow weather for 30 seconds (no rain)
                    level.setWeatherParameters(0, 30 * 20, false, true); // 30 seconds of snow only
                }
            }

            // Spawn lots of snow particles around players for heavy snow effect
            for (int i = 0; i < 70; i++) { // 70 iterations over 35 seconds (every 0.5 seconds)
                scheduler.schedule(() -> {
                    for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                        ServerLevel level = player.serverLevel();
                        BlockPos playerPos = player.blockPosition();

                        // Spawn MASSIVE amounts of snow particles for heavy visibility reduction
                        for (int j = 0; j < 500; j++) { // 500 particles per player per wave (5x more)
                            // Large area around player for heavy snow effect
                            double x = playerPos.getX() + (random.nextDouble() - 0.5) * 60; // 60 block radius (larger)
                            double z = playerPos.getZ() + (random.nextDouble() - 0.5) * 60;
                            double y = playerPos.getY() + 5 + random.nextDouble() * 30; // 5-35 blocks above

                            // Spawn dense snow particles falling down
                            level.sendParticles(ParticleTypes.SNOWFLAKE,
                                x, y, z,
                                10, // particle count (doubled)
                                0.8, 0.0, 0.8, // spread (increased)
                                0.2); // speed (increased)
                        }

                        // Add extra dense snow particles close to player for immediate visibility reduction
                        for (int k = 0; k < 200; k++) { // 200 close particles
                            double closeX = playerPos.getX() + (random.nextDouble() - 0.5) * 12; // 12 block radius around player
                            double closeY = playerPos.getY() + random.nextDouble() * 8; // 8 blocks above
                            double closeZ = playerPos.getZ() + (random.nextDouble() - 0.5) * 12;

                            // Very dense snow particles right around the player
                            level.sendParticles(ParticleTypes.SNOWFLAKE, closeX, closeY, closeZ, 15, 0.5, 0.5, 0.5, 0.3);

                            // Add white ash particles for extra density
                            if (k % 2 == 0) { // Every other particle
                                level.sendParticles(ParticleTypes.WHITE_ASH, closeX, closeY, closeZ, 8, 0.3, 0.3, 0.3, 0.1);
                            }
                        }

                        // No sound effects for now to avoid compatibility issues
                    }

                    // Show periodic snow messages (removed unwanted messages)
                    if (random.nextInt(25) == 0) { // 4% chance each wave
                        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                            String[] snowMessages = {
                                "§f§lHEAVY SNOW FALLING!",
                                "§f§lCAN'T SEE THROUGH THE SNOW!",
                                "§7§lSNOW EVERYWHERE!",
                                "§7§lTOO MUCH SNOW!"
                            };
                            String message = snowMessages[random.nextInt(snowMessages.length)];
                            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                                net.minecraft.network.chat.Component.literal(message)));
                        }
                    }
                }, i * 500L, TimeUnit.MILLISECONDS); // Every 0.5 seconds for heavy snow effect
            }

            // Clear weather and set day time after 35 seconds
            scheduler.schedule(() -> {
                // Clear the weather and set to day time
                for (ServerLevel level : server.getAllLevels()) {
                    if (level.dimension() == ServerLevel.OVERWORLD) {
                        level.setWeatherParameters(6000, 0, false, false); // Clear weather
                        level.setDayTime(1000L); // Set to day time (1000 = morning)
                    }
                }

                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                        net.minecraft.network.chat.Component.literal("§7§lThe snow is clearing... §eThe sun is coming out!")));
                }
            }, 35L, TimeUnit.SECONDS);

        }, 3L, TimeUnit.SECONDS); // 3 second delay after warning
    }
}
