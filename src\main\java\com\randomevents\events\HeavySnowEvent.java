package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;

import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class HeavySnowEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    
    @Override
    public String getId() {
        return "heavy_snow";
    }
    
    @Override
    public String getName() {
        return "§f§lHeavy Snow!";
    }
    
    @Override
    public String getDescription() {
        return "A LOT of snow falls from the sky, reducing visibility for 45 seconds";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§f§lThe sky is getting dark... §7A blizzard is coming!")));
        }

        // Wait 3 seconds then start the heavy snow weather
        scheduler.schedule(() -> {
            // Set heavy snow weather for all overworld levels
            for (ServerLevel level : server.getAllLevels()) {
                if (level.dimension() == ServerLevel.OVERWORLD) {
                    // Set intense rain/snow weather for 45 seconds
                    level.setWeatherParameters(0, 45 * 20, true, false); // 45 seconds of rain/snow
                }
            }

            // Spawn lots of snow particles around players for heavy snow effect
            for (int i = 0; i < 90; i++) { // 90 iterations over 45 seconds (every 0.5 seconds)
                scheduler.schedule(() -> {
                    for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                        ServerLevel level = player.serverLevel();
                        BlockPos playerPos = player.blockPosition();

                        // Spawn massive amounts of snow particles around each player
                        for (int j = 0; j < 100; j++) { // 100 particles per player per wave
                            // Large area around player for heavy snow effect
                            double x = playerPos.getX() + (random.nextDouble() - 0.5) * 40; // 40 block radius
                            double z = playerPos.getZ() + (random.nextDouble() - 0.5) * 40;
                            double y = playerPos.getY() + 10 + random.nextDouble() * 20; // 10-30 blocks above

                            // Spawn snow particles falling down
                            level.sendParticles(ParticleTypes.SNOWFLAKE,
                                x, y, z,
                                5, // particle count
                                0.5, 0.0, 0.5, // spread
                                0.1); // speed
                        }

                        // Play wind/snow sounds occasionally
                        if (random.nextInt(10) == 0) { // 10% chance
                            level.playSound(null, playerPos, SoundEvents.WEATHER_RAIN, SoundSource.WEATHER, 0.8f, 0.8f);
                        }
                    }

                    // Show periodic snow messages
                    if (random.nextInt(20) == 0) { // 5% chance each wave
                        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                            String[] snowMessages = {
                                "§f§lHEAVY SNOW FALLING!",
                                "§7§lBLIZZARD CONDITIONS!",
                                "§f§lCAN'T SEE THROUGH THE SNOW!",
                                "§7§lSNOW EVERYWHERE!",
                                "§f§lWHITE OUT CONDITIONS!",
                                "§7§lTOO MUCH SNOW!",
                                "§f§lBLIZZARD RAGING!"
                            };
                            String message = snowMessages[random.nextInt(snowMessages.length)];
                            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                                net.minecraft.network.chat.Component.literal(message)));
                        }
                    }
                }, i * 500L, TimeUnit.MILLISECONDS); // Every 0.5 seconds for heavy snow effect
            }

            // Clear weather and show end message after 45 seconds
            scheduler.schedule(() -> {
                // Clear the weather
                for (ServerLevel level : server.getAllLevels()) {
                    if (level.dimension() == ServerLevel.OVERWORLD) {
                        level.setWeatherParameters(6000, 0, false, false); // Clear weather
                    }
                }

                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                        net.minecraft.network.chat.Component.literal("§7§lThe blizzard is clearing... §fThe snow is settling.")));
                }
            }, 45L, TimeUnit.SECONDS);

        }, 3L, TimeUnit.SECONDS); // 3 second delay after warning
    }
}
