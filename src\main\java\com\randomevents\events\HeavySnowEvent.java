package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.item.FallingBlockEntity;
import net.minecraft.world.level.block.Blocks;

import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class HeavySnowEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    private static volatile boolean eventActive = false;
    private static final int MAX_SNOW_PER_WAVE = 50; // Lots of snow per wave
    private static final int MAX_TOTAL_SNOW = 2000; // Total limit for entire event
    private static int totalSnowSpawned = 0;
    
    @Override
    public String getId() {
        return "heavy_snow";
    }
    
    @Override
    public String getName() {
        return "§f§lHeavy Snow!";
    }
    
    @Override
    public String getDescription() {
        return "A LOT of snow falls from the sky, reducing visibility for 45 seconds";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        // Reset counters and activate event
        eventActive = true;
        totalSnowSpawned = 0;

        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§f§lThe sky is getting dark... §7A blizzard is coming!")));
        }
        
        // Wait 3 seconds then start the heavy snow
        scheduler.schedule(() -> {
            // Heavy snow for 45 seconds (every 0.5 seconds) with safety limits
            for (int i = 0; i < 90; i++) { // 90 iterations over 45 seconds (every 0.5 seconds)
                scheduler.schedule(() -> {
                    if (!eventActive || totalSnowSpawned >= MAX_TOTAL_SNOW) {
                        return; // Stop if event ended or hit limit
                    }

                    int snowThisWave = 0;
                    for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                        if (snowThisWave >= MAX_SNOW_PER_WAVE || totalSnowSpawned >= MAX_TOTAL_SNOW) {
                            break; // Stop this wave if we hit limits
                        }

                        ServerLevel level = player.serverLevel();
                        BlockPos playerPos = player.blockPosition();
                        
                        // Spawn lots of snow blocks around each player
                        int snowCount = 15 + random.nextInt(11); // 15-25 snow blocks per player per wave
                        for (int j = 0; j < snowCount; j++) {
                            if (snowThisWave >= MAX_SNOW_PER_WAVE || totalSnowSpawned >= MAX_TOTAL_SNOW) {
                                break;
                            }
                            
                            // Large area around player for heavy snow effect
                            int x = playerPos.getX() + random.nextInt(60) - 30; // 60 block radius
                            int z = playerPos.getZ() + random.nextInt(60) - 30;
                            int y = level.getHeight(net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING, x, z) + 15 + random.nextInt(20);
                            
                            BlockPos snowPos = new BlockPos(x, y, z);

                            // Create falling snow block with safety checks
                            if (level.isInWorldBounds(snowPos)) {
                                try {
                                    // Create falling snow block
                                    FallingBlockEntity fallingSnow = FallingBlockEntity.fall(level, snowPos, Blocks.SNOW_BLOCK.defaultBlockState());
                                    if (fallingSnow != null) {
                                        // Make snow fall at normal speed with slight randomness
                                        fallingSnow.setDeltaMovement(
                                            (random.nextDouble() - 0.5) * 0.1, // Slight horizontal drift
                                            -0.5 - random.nextDouble() * 0.3, // Varied fall speed
                                            (random.nextDouble() - 0.5) * 0.1
                                        );

                                        snowThisWave++;
                                        totalSnowSpawned++;
                                        // Note: The entity is already added to the level by FallingBlockEntity.fall()
                                    }
                                } catch (Exception e) {
                                    // Silently handle any entity creation errors to prevent crashes
                                }
                            }
                        }
                    }
                    
                    // Show periodic snow messages
                    if (random.nextInt(20) == 0) { // 5% chance each wave
                        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                            String[] snowMessages = {
                                "§f§lHEAVY SNOW FALLING!",
                                "§7§lBLIZZARD CONDITIONS!",
                                "§f§lCAN'T SEE THROUGH THE SNOW!",
                                "§7§lSNOW EVERYWHERE!",
                                "§f§lWHITE OUT CONDITIONS!",
                                "§7§lTOO MUCH SNOW!",
                                "§f§lBLIZZARD RAGING!"
                            };
                            String message = snowMessages[random.nextInt(snowMessages.length)];
                            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                                net.minecraft.network.chat.Component.literal(message)));
                        }
                    }
                }, i * 500L, TimeUnit.MILLISECONDS); // Every 0.5 seconds for heavy snow effect
            }

            // Show end message after 45 seconds and deactivate event
            scheduler.schedule(() -> {
                eventActive = false;
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                        net.minecraft.network.chat.Component.literal("§7§lThe blizzard is clearing... §f(" + totalSnowSpawned + " snow blocks total)")));
                }
            }, 45L, TimeUnit.SECONDS);
            
        }, 3L, TimeUnit.SECONDS); // 3 second delay after warning
    }
}
