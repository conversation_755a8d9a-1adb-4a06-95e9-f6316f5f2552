package com.randomevents.events;

import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.item.ItemEntity;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.phys.Vec3;

import java.util.Random;

public class DropHeldItemEvent extends RandomEvent {
    private static final Random random = new Random();
    
    @Override
    public String getId() {
        return "drop_held_item";
    }
    
    @Override
    public String getName() {
        return "§c§lDrop Held Item!";
    }
    
    @Override
    public String getDescription() {
        return "Force the player to drop the item they're holding. It flies 10 blocks away.";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            dropPlayerHeldItem(player);
        }
    }
    
    private void dropPlayerHeldItem(ServerPlayer player) {
        ItemStack heldItem = player.getMainHandItem();
        
        // Check if player is holding something
        if (!heldItem.isEmpty()) {
            ServerLevel level = player.serverLevel();
            
            // Remove item from player's hand
            player.getInventory().setItem(player.getInventory().selected, ItemStack.EMPTY);
            
            // Create item entity
            ItemEntity itemEntity = new ItemEntity(level, player.getX(), player.getY() + 1.0, player.getZ(), heldItem);
            
            // Calculate random direction and launch it 10 blocks away
            double angle = random.nextDouble() * 2 * Math.PI; // Random angle
            double velocityX = Math.cos(angle) * 1.5; // Velocity to reach ~10 blocks
            double velocityY = 0.5; // Slight upward velocity
            double velocityZ = Math.sin(angle) * 1.5;
            
            // Set the velocity to make it fly away
            itemEntity.setDeltaMovement(new Vec3(velocityX, velocityY, velocityZ));
            
            // Add to world
            level.addFreshEntity(itemEntity);
            
            // Show message
            String itemName = heldItem.getDisplayName().getString();
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§c§lYour " + itemName + " flew away!")));
        } else {
            // Player wasn't holding anything
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§7§lYou weren't holding anything to drop!")));
        }
    }
}
