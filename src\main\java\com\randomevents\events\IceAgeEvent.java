package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class IceAgeEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    
    @Override
    public String getId() {
        return "ice_age";
    }
    
    @Override
    public String getName() {
        return "§b§lIce Age!";
    }
    
    @Override
    public String getDescription() {
        return "Ice slowly spreads across the ground toward players over 30 seconds";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            startIceSpread(player);
        }
    }

    private void startIceSpread(ServerPlayer player) {
        ServerLevel level = player.serverLevel();
        BlockPos playerPos = player.blockPosition();

        // Spread ice in expanding rings over 30 seconds
        // Start with radius 1 and expand to radius 50 over 30 seconds
        for (int wave = 1; wave <= 30; wave++) {
            final int currentWave = wave;

            scheduler.schedule(() -> {
                // Calculate radius for this wave (accelerating expansion)
                int radius = (int) Math.pow(currentWave * 1.8, 1.2); // Accelerating growth
                if (radius > 50) radius = 50; // Cap at 50 blocks

                // Spread ice in a ring pattern
                spreadIceRing(level, playerPos, radius);

            }, wave * 1L, TimeUnit.SECONDS); // Every second for 30 seconds
        }
    }

    private void spreadIceRing(ServerLevel level, BlockPos centerPos, int radius) {
        // Create ice in a ring pattern around the center, focusing on floor underneath players
        for (int x = -radius; x <= radius; x++) {
            for (int z = -radius; z <= radius; z++) {
                // Only process blocks at the edge of the current radius (ring effect)
                double distance = Math.sqrt(x * x + z * z);
                if (distance >= radius - 3 && distance <= radius) { // 3-block thick ring for better coverage

                    // Focus on floor blocks (1-2 blocks under player level)
                    for (int y = -2; y <= 0; y++) { // Floor level blocks
                        BlockPos checkPos = centerPos.offset(x, y, z);

                        if (level.isInWorldBounds(checkPos)) {
                            BlockState blockState = level.getBlockState(checkPos);

                            // Turn water into ice
                            if (blockState.is(Blocks.WATER)) {
                                level.setBlock(checkPos, Blocks.ICE.defaultBlockState(), 2);
                            }
                            // Turn ground blocks into ice (focus on floor level)
                            else if (blockState.is(Blocks.STONE) || blockState.is(Blocks.DIRT) ||
                                    blockState.is(Blocks.GRASS_BLOCK) || blockState.is(Blocks.COBBLESTONE) ||
                                    blockState.is(Blocks.SAND) || blockState.is(Blocks.GRAVEL) ||
                                    blockState.is(Blocks.ANDESITE) || blockState.is(Blocks.GRANITE) ||
                                    blockState.is(Blocks.DIORITE) || blockState.is(Blocks.DEEPSLATE) ||
                                    blockState.is(Blocks.NETHERRACK) || blockState.is(Blocks.END_STONE) ||
                                    blockState.is(Blocks.SANDSTONE) || blockState.is(Blocks.RED_SANDSTONE)) {
                                // Use flag 2 to prevent block updates and reduce tick load
                                level.setBlock(checkPos, Blocks.ICE.defaultBlockState(), 2);
                            }
                        }
                    }
                }
            }
        }
    }
}
