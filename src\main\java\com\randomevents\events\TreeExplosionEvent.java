package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.item.PrimedTnt;
import net.minecraft.world.level.block.Blocks;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import net.minecraft.world.level.block.RotatedPillarBlock;

import java.util.Random;

public class TreeExplosionEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    
    @Override
    public String getId() {
        return "tree_explosion";
    }
    
    @Override
    public String getName() {
        return "§2§lTree Explosion!";
    }
    
    @Override
    public String getDescription() {
        return "All tree logs near players turn into primed TNT";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        // Show warning message for 5 seconds
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§c§lI hope you're not near any trees...")));
        }

        // After 5 seconds, proceed with the tree explosion
        scheduler.schedule(() -> {
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                ServerLevel level = player.serverLevel();
                BlockPos playerPos = player.blockPosition();

                // Check all blocks in a 30x30x30 area around the player
                for (int x = -15; x <= 15; x++) {
                    for (int y = -15; y <= 15; y++) {
                        for (int z = -15; z <= 15; z++) {
                            BlockPos checkPos = playerPos.offset(x, y, z);

                            if (level.isInWorldBounds(checkPos)) {
                                // Check if the block is a log block (wood)
                                if (isLogBlock(level.getBlockState(checkPos).getBlock())) {
                                    // Replace the log with air
                                    level.setBlock(checkPos, Blocks.AIR.defaultBlockState(), 3);

                                    // Spawn primed TNT at that location
                                    PrimedTnt tnt = EntityType.TNT.create(level);
                                    if (tnt != null) {
                                        tnt.moveTo(checkPos.getX() + 0.5, checkPos.getY(), checkPos.getZ() + 0.5);
                                        tnt.setFuse(40 + level.getRandom().nextInt(60)); // 2-5 seconds fuse
                                        level.addFreshEntity(tnt);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }, 5L, TimeUnit.SECONDS);
    }

    private boolean isLogBlock(net.minecraft.world.level.block.Block block) {
        return block == Blocks.OAK_LOG ||
               block == Blocks.BIRCH_LOG ||
               block == Blocks.SPRUCE_LOG ||
               block == Blocks.JUNGLE_LOG ||
               block == Blocks.ACACIA_LOG ||
               block == Blocks.DARK_OAK_LOG ||
               block == Blocks.MANGROVE_LOG ||
               block == Blocks.CHERRY_LOG ||
               block == Blocks.STRIPPED_OAK_LOG ||
               block == Blocks.STRIPPED_BIRCH_LOG ||
               block == Blocks.STRIPPED_SPRUCE_LOG ||
               block == Blocks.STRIPPED_JUNGLE_LOG ||
               block == Blocks.STRIPPED_ACACIA_LOG ||
               block == Blocks.STRIPPED_DARK_OAK_LOG ||
               block == Blocks.STRIPPED_MANGROVE_LOG ||
               block == Blocks.STRIPPED_CHERRY_LOG ||
               block == Blocks.OAK_WOOD ||
               block == Blocks.BIRCH_WOOD ||
               block == Blocks.SPRUCE_WOOD ||
               block == Blocks.JUNGLE_WOOD ||
               block == Blocks.ACACIA_WOOD ||
               block == Blocks.DARK_OAK_WOOD ||
               block == Blocks.MANGROVE_WOOD ||
               block == Blocks.CHERRY_WOOD ||
               block == Blocks.STRIPPED_OAK_WOOD ||
               block == Blocks.STRIPPED_BIRCH_WOOD ||
               block == Blocks.STRIPPED_SPRUCE_WOOD ||
               block == Blocks.STRIPPED_JUNGLE_WOOD ||
               block == Blocks.STRIPPED_ACACIA_WOOD ||
               block == Blocks.STRIPPED_DARK_OAK_WOOD ||
               block == Blocks.STRIPPED_MANGROVE_WOOD ||
               block == Blocks.STRIPPED_CHERRY_WOOD;
    }
}
