package com.randomevents.events;

import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.item.TieredItem;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class AllToolsArePickaxesEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Map<ServerPlayer, ItemStack[]> originalHotbars = new HashMap<>();
    
    @Override
    public String getId() {
        return "all_tools_are_pickaxes";
    }
    
    @Override
    public String getName() {
        return "§8§lAll Tools Are Pickaxes!";
    }
    
    @Override
    public String getDescription() {
        return "Temporarily replace all tools in the player's hotbar with stone pickaxes for 30 seconds";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        originalHotbars.clear();
        
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            transformPlayerHotbar(player);
        }
        
        // Show message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§8§lAll your tools are now pickaxes for 30 seconds!")));
        }
        
        // Restore original tools after 30 seconds
        scheduler.schedule(() -> {
            restoreOriginalHotbars(server);
        }, 30L, TimeUnit.SECONDS);
    }
    
    private void transformPlayerHotbar(ServerPlayer player) {
        // Store original hotbar
        ItemStack[] originalHotbar = new ItemStack[9];
        for (int i = 0; i < 9; i++) {
            originalHotbar[i] = player.getInventory().getItem(i).copy();
        }
        originalHotbars.put(player, originalHotbar);
        
        // Replace all tools with stone pickaxes (preserving names)
        for (int i = 0; i < 9; i++) {
            ItemStack currentItem = player.getInventory().getItem(i);
            
            if (!currentItem.isEmpty() && isTool(currentItem)) {
                // Create stone pickaxe with preserved name
                ItemStack stonePickaxe = new ItemStack(Items.STONE_PICKAXE);
                
                // Preserve the original item's display name if it has one
                if (currentItem.hasCustomHoverName()) {
                    stonePickaxe.setHoverName(currentItem.getHoverName());
                }
                
                // Preserve enchantments if any
                if (currentItem.isEnchanted()) {
                    stonePickaxe.setTag(currentItem.getTag().copy());
                }
                
                // Replace the item
                player.getInventory().setItem(i, stonePickaxe);
            }
        }
    }
    
    private boolean isTool(ItemStack item) {
        // Check if the item is a tool (pickaxe, axe, shovel, hoe, sword)
        return item.getItem() instanceof TieredItem ||
               item.getItem() == Items.WOODEN_PICKAXE ||
               item.getItem() == Items.WOODEN_AXE ||
               item.getItem() == Items.WOODEN_SHOVEL ||
               item.getItem() == Items.WOODEN_HOE ||
               item.getItem() == Items.WOODEN_SWORD ||
               item.getItem() == Items.STONE_PICKAXE ||
               item.getItem() == Items.STONE_AXE ||
               item.getItem() == Items.STONE_SHOVEL ||
               item.getItem() == Items.STONE_HOE ||
               item.getItem() == Items.STONE_SWORD ||
               item.getItem() == Items.IRON_PICKAXE ||
               item.getItem() == Items.IRON_AXE ||
               item.getItem() == Items.IRON_SHOVEL ||
               item.getItem() == Items.IRON_HOE ||
               item.getItem() == Items.IRON_SWORD ||
               item.getItem() == Items.GOLDEN_PICKAXE ||
               item.getItem() == Items.GOLDEN_AXE ||
               item.getItem() == Items.GOLDEN_SHOVEL ||
               item.getItem() == Items.GOLDEN_HOE ||
               item.getItem() == Items.GOLDEN_SWORD ||
               item.getItem() == Items.DIAMOND_PICKAXE ||
               item.getItem() == Items.DIAMOND_AXE ||
               item.getItem() == Items.DIAMOND_SHOVEL ||
               item.getItem() == Items.DIAMOND_HOE ||
               item.getItem() == Items.DIAMOND_SWORD ||
               item.getItem() == Items.NETHERITE_PICKAXE ||
               item.getItem() == Items.NETHERITE_AXE ||
               item.getItem() == Items.NETHERITE_SHOVEL ||
               item.getItem() == Items.NETHERITE_HOE ||
               item.getItem() == Items.NETHERITE_SWORD;
    }
    
    private void restoreOriginalHotbars(MinecraftServer server) {
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            if (originalHotbars.containsKey(player)) {
                ItemStack[] originalHotbar = originalHotbars.get(player);
                
                // Restore original hotbar
                for (int i = 0; i < 9; i++) {
                    player.getInventory().setItem(i, originalHotbar[i]);
                }
                
                // Show restoration message
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§a§lYour original tools have been restored!")));
            }
        }
        
        // Clear stored data
        originalHotbars.clear();
    }
}
