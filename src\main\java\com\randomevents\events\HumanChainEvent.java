package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;

import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class HumanChainEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    
    @Override
    public String getId() {
        return "human_chain";
    }
    
    @Override
    public String getName() {
        return "§e§lHuman Chain!";
    }
    
    @Override
    public String getDescription() {
        return "Players must stay within 5 blocks of each other or take damage for 30 seconds";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§e§lYou might want to stay close to each other...")));
        }
        
        // After 5 seconds, teleport all players together and start the event
        scheduler.schedule(() -> {
            List<ServerPlayer> players = server.getPlayerList().getPlayers();
            if (players.isEmpty()) return;
            
            // Find the first player's position as the meeting point
            ServerPlayer firstPlayer = players.get(0);
            BlockPos meetingPoint = firstPlayer.blockPosition();
            ServerLevel level = firstPlayer.serverLevel();
            
            // Teleport all players to the meeting point
            for (int i = 0; i < players.size(); i++) {
                ServerPlayer player = players.get(i);
                if (player.isAlive() && player.getServer() != null) {
                    // Spread players out slightly so they don't overlap
                    double offsetX = (i % 3 - 1) * 1.5; // -1.5, 0, 1.5
                    double offsetZ = (i / 3 - 1) * 1.5;
                    
                    player.teleportTo(level, 
                        meetingPoint.getX() + offsetX + 0.5, 
                        meetingPoint.getY(), 
                        meetingPoint.getZ() + offsetZ + 0.5, 
                        player.getYRot(), player.getXRot());
                    
                    // Play teleport sound
                    level.playSound(null, player.blockPosition(), SoundEvents.ENDERMAN_TELEPORT, SoundSource.PLAYERS, 1.0f, 1.0f);
                }
            }
            
            // Start the human chain damage check for 30 seconds (60 checks, every 0.5 seconds)
            for (int i = 0; i < 60; i++) {
                scheduler.schedule(() -> {
                    checkPlayerDistances(server);
                }, i * 500L, TimeUnit.MILLISECONDS);
            }
            
        }, 5L, TimeUnit.SECONDS);
    }
    
    private void checkPlayerDistances(MinecraftServer server) {
        List<ServerPlayer> players = server.getPlayerList().getPlayers();
        if (players.size() < 2) return;
        
        for (ServerPlayer player : players) {
            if (!player.isAlive()) continue;
            
            boolean hasNearbyPlayer = false;
            
            // Check if this player has at least one other player within 5 blocks
            for (ServerPlayer otherPlayer : players) {
                if (otherPlayer == player || !otherPlayer.isAlive()) continue;
                
                // Check if they're in the same dimension
                if (player.serverLevel() != otherPlayer.serverLevel()) continue;
                
                double distance = player.distanceTo(otherPlayer);
                if (distance <= 5.0) {
                    hasNearbyPlayer = true;
                    break;
                }
            }
            
            // If no nearby player, deal half a heart of damage
            if (!hasNearbyPlayer) {
                player.hurt(player.serverLevel().damageSources().magic(), 1.0f); // 1.0f = half a heart
                
                // Show warning message
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§c§lSTAY CLOSE TO YOUR TEAM! §4§l-0.5 Hearts!")));
                
                // Play hurt sound
                player.serverLevel().playSound(null, player.blockPosition(), SoundEvents.PLAYER_HURT, SoundSource.PLAYERS, 0.5f, 1.0f);
            }
        }
    }
}
