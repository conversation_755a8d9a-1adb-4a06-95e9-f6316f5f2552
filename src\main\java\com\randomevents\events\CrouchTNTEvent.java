package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.item.PrimedTnt;



import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class CrouchTNTEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static boolean crouchTNTActive = false;
    private static final Map<ServerPlayer, Boolean> playerCrouchState = new HashMap<>();
    private static final Map<ServerPlayer, Long> playerTNTCooldown = new HashMap<>(); // Track TNT spawn cooldown
    
    @Override
    public String getId() {
        return "crouch_tnt";
    }
    
    @Override
    public String getName() {
        return "§c§lNo Running or Walking!";
    }
    
    @Override
    public String getDescription() {
        return "Crouching OR sprinting (CTRL+running) summons TNT that explodes 2 blocks in front of you for 35 seconds";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        playerCrouchState.clear();
        playerTNTCooldown.clear();

        // Show warning message for 5 seconds
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§4§lYou don't wanna see what happens when you run or walk...")));
            playerCrouchState.put(player, false);
            playerTNTCooldown.put(player, 0L); // Initialize cooldown
        }

        // After 5 seconds, activate the TNT mechanics
        scheduler.schedule(() -> {
            crouchTNTActive = true;

            // Show the real warning
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§c§lRUN OR WALK = TNT! §7Don't move fast!")));
            }

            // Check for running/walking every 0.5 seconds for 35 seconds (reduced server load)
            for (int i = 0; i < 70; i++) { // 35 seconds / 0.5 seconds = 70 checks
                scheduler.schedule(() -> {
                    if (crouchTNTActive) {
                        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                            try {
                                boolean isSprinting = player.isSprinting(); // Running (CTRL+W)
                                boolean isWalking = player.getDeltaMovement().horizontalDistance() > 0.1; // Walking (W/A/S/D)
                                long currentTime = System.currentTimeMillis();
                                long lastTNTTime = playerTNTCooldown.getOrDefault(player, 0L);

                                // Check if player is running or walking and cooldown has passed
                                if ((isSprinting || isWalking) && (currentTime - lastTNTTime) >= 2000) { // 2 second cooldown
                                    spawnTNTInFrontOfPlayer(player);
                                    playerTNTCooldown.put(player, currentTime); // Update cooldown
                                }
                            } catch (Exception e) {
                                // Prevent crashes
                                System.err.println("Error in No Running or Walking event: " + e.getMessage());
                            }
                        }
                    }
                }, i * 500L, TimeUnit.MILLISECONDS); // Every 500ms (0.5 seconds) (5 ticks)
            }

            // Stop effect after 35 seconds
            scheduler.schedule(() -> {
                crouchTNTActive = false;
                playerCrouchState.clear();
                playerTNTCooldown.clear();
            }, 35L, TimeUnit.SECONDS);

        }, 5L, TimeUnit.SECONDS); // Wait 5 seconds before starting TNT mechanics
    }

    private void spawnTNTInFrontOfPlayer(ServerPlayer player) {
        // Player just started crouching - spawn TNT!
        ServerLevel level = player.serverLevel();

        // Calculate position 2 blocks in front of player
        double yaw = Math.toRadians(player.getYRot());
        double x = player.getX() - Math.sin(yaw) * 2.0;
        double z = player.getZ() + Math.cos(yaw) * 2.0;
        double y = player.getY();

        // Make sure TNT spawns on solid ground
        BlockPos groundPos = new BlockPos((int)x, (int)y - 1, (int)z);
        while (level.getBlockState(groundPos).isAir() && groundPos.getY() > level.getMinBuildHeight()) {
            groundPos = groundPos.below();
        }

        // Spawn TNT on the ground
        double tntY = groundPos.getY() + 1;

        // Spawn primed TNT
        PrimedTnt tnt = new PrimedTnt(level, x, tntY, z, player);
        tnt.setFuse(15); // 0.75 seconds fuse
        level.addFreshEntity(tnt);

        // Show message
        player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
            net.minecraft.network.chat.Component.literal("§c§l💥 TNT SUMMONED! §7RUN!")));

        // Also show to other players
        for (ServerPlayer otherPlayer : level.getServer().getPlayerList().getPlayers()) {
            if (otherPlayer != player && otherPlayer.distanceToSqr(player) < 400) { // Within 20 blocks
                otherPlayer.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§c§l" + player.getName().getString() + " summoned TNT!")));
            }
        }
    }
}
