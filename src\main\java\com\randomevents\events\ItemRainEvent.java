package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;

import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class ItemRainEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    
    // Array of random items to rain down
    private static final net.minecraft.world.item.Item[] RAIN_ITEMS = {
        Items.STICK, Items.STONE, Items.DIRT, Items.COBBLESTONE, Items.OAK_PLANKS,
        Items.APPLE, Items.BREAD, Items.CARROT, Items.POTATO, Items.WHEAT,
        Items.IRON_INGOT, Items.GOLD_INGOT, Items.DIAMOND, Items.EMERALD, Items.COAL,
        Items.ARROW, Items.BOW, Items.FISHING_ROD, Items.BUCKET, Items.SHEARS,
        Items.LEATHER, Items.FEATHER, Items.STRING, Items.BONE, Items.GUNPOWDER,
        Items.REDSTONE, Items.GLOWSTONE_DUST, Items.ENDER_PEARL, Items.BLAZE_ROD, Items.GHAST_TEAR
    };
    
    @Override
    public String getId() {
        return "item_rain";
    }
    
    @Override
    public String getName() {
        return "§e§lItem Rain!";
    }
    
    @Override
    public String getDescription() {
        return "MASSIVE amounts of random items fall from the sky for 2 minutes";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        // Show single message and keep it for 4 seconds
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§e§lMassive Item Rain! §7Items falling from the sky!")));
        }

        // Clear the message after 4 seconds
        scheduler.schedule(() -> {
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("")));
            }
        }, 4L, TimeUnit.SECONDS);

        // Rain items for 30 seconds (every 0.5 seconds)
        for (int i = 0; i < 60; i++) { // 30 seconds * 2 drops per second = 60 drops
            scheduler.schedule(() -> {
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    ServerLevel level = player.serverLevel();
                    BlockPos playerPos = player.blockPosition();

                    // Drop 6-14 random items around each player (doubled from 3-7)
                    int itemCount = 6 + random.nextInt(9);
                    for (int j = 0; j < itemCount; j++) {
                        int x = playerPos.getX() + random.nextInt(80) - 40; // Larger area
                        int z = playerPos.getZ() + random.nextInt(80) - 40;
                        int y = level.getHeight(net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING, x, z) + 20 + random.nextInt(15);

                        BlockPos dropPos = new BlockPos(x, y, z);

                        // Choose random item
                        net.minecraft.world.item.Item randomItem = RAIN_ITEMS[random.nextInt(RAIN_ITEMS.length)];
                        ItemStack itemStack = new ItemStack(randomItem, 1 + random.nextInt(5)); // 1-5 items

                        // Drop the item
                        if (level.isInWorldBounds(dropPos)) {
                            net.minecraft.world.entity.item.ItemEntity itemEntity =
                                new net.minecraft.world.entity.item.ItemEntity(level,
                                    dropPos.getX() + 0.5, dropPos.getY(), dropPos.getZ() + 0.5, itemStack);

                            // Make items fall faster
                            itemEntity.setDeltaMovement(0, -0.8, 0);
                            level.addFreshEntity(itemEntity);
                        }
                    }
                }

                // No more message spam - keep it clean
            }, i * 500L, TimeUnit.MILLISECONDS); // Every 0.5 seconds
        }

        // Show end message after 30 seconds
        scheduler.schedule(() -> {
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§7§lThe item rain has ended. §aCollect your loot!")));
            }
        }, 32L, TimeUnit.SECONDS); // 2 seconds after the last drop
    }
}
