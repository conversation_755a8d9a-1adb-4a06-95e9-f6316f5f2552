package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;

import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class ColorMatchPuzzleEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    
    // Color blocks for the puzzle (smaller selection for 3x3)
    private static final Block[] COLOR_BLOCKS = {
        Blocks.RED_WOOL, Blocks.BLUE_WOOL, Blocks.GREEN_WOOL, Blocks.YELLOW_WOOL
    };

    private static final Map<BlockPos, BlockState> originalBlocks = new HashMap<>();
    private static final Map<BlockPos, Block> targetPattern = new HashMap<>();
    private static final Map<ServerPlayer, BlockPos> originalPlayerPositions = new HashMap<>();
    private static BlockPos puzzleCenter;
    private static BlockPos playerPlatform;
    private static boolean puzzleActive = false;
    
    @Override
    public String getId() {
        return "color_match_puzzle";
    }
    
    @Override
    public String getName() {
        return "§e§lColor Match Puzzle!";
    }
    
    @Override
    public String getDescription() {
        return "Work together to recreate the color pattern or everyone explodes!";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        originalBlocks.clear();
        targetPattern.clear();
        originalPlayerPositions.clear();
        puzzleActive = true;

        // Pause the main event timer (will resume when puzzle ends)
        // TODO: Add pause functionality to RandomEventManager

        List<ServerPlayer> players = server.getPlayerList().getPlayers();
        if (players.isEmpty()) return;

        // Save original positions for all players
        for (ServerPlayer player : players) {
            originalPlayerPositions.put(player, player.blockPosition());
        }

        ServerLevel level = server.overworld();

        // Create puzzle high in the sky to avoid damaged structures
        ServerPlayer firstPlayer = players.get(0);
        BlockPos basePos = firstPlayer.blockPosition();

        // Set up sky location (150+ blocks up)
        int skyY = Math.max(150, level.getMaxBuildHeight() - 50);

        // Player platform in the sky
        playerPlatform = new BlockPos(basePos.getX(), skyY, basePos.getZ());

        // Puzzle wall 8 blocks in front of players (Z direction so it faces them)
        puzzleCenter = playerPlatform.offset(0, 0, 8);

        // Create sky platforms and vertical wall grid
        createSkyPlatformAndWall(level);

        // Teleport all players to the sky platform facing the wall
        for (int i = 0; i < players.size(); i++) {
            ServerPlayer player = players.get(i);

            // Position players on the platform in a line facing the wall
            double spacing = 2.0; // Space between players
            double x = playerPlatform.getX() + (i - players.size() / 2.0) * spacing; // Spread them out on platform
            double z = playerPlatform.getZ(); // On the platform
            double y = playerPlatform.getY() + 1; // One block above platform

            player.teleportTo(level, x + 0.5, y, z + 0.5, 90, 0); // Face the wall (90 degrees)

            // Give players the blocks they need
            givePlayerBlocks(player);

            // Play teleport sound
            level.playSound(null, player.blockPosition(), SoundEvents.ENDERMAN_TELEPORT, SoundSource.PLAYERS, 1.0f, 1.0f);
        }

        // Show cooperation message
        for (ServerPlayer player : players) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§e§lLook ahead! Work together to recreate the pattern on the stone grid!")));
        }

        // Wait 3 seconds then start the puzzle
        scheduler.schedule(() -> {
            showTargetPattern(server);
        }, 3L, TimeUnit.SECONDS);
    }
    
    private void createSkyPlatformAndWall(ServerLevel level) {
        // Create player platform (7x7 obsidian platform in the sky)
        for (int x = -3; x <= 3; x++) {
            for (int z = -3; z <= 3; z++) {
                BlockPos pos = playerPlatform.offset(x, 0, z);
                level.setBlock(pos, Blocks.OBSIDIAN.defaultBlockState(), 3);
            }
        }

        // Stone brick wall removed as requested - only keeping stone solution grid

        // Create white backwall BEHIND the stone solution grid for puzzle pattern display (raised by 2)
        for (int x = -1; x <= 1; x++) {
            for (int y = 2; y <= 4; y++) { // Raised by 2 Y levels
                BlockPos backwallPos = puzzleCenter.offset(x, y, -4); // BEHIND the stone solution grid (at -4, solution grid is at -3)
                level.setBlock(backwallPos, Blocks.WHITE_WOOL.defaultBlockState(), 3);
            }
        }

        // Display grid is now the white backwall - no separate display grid needed

        // Create SOLUTION grid (where players place blocks) - closer to players, raised by 2
        for (int x = -1; x <= 1; x++) {
            for (int y = 2; y <= 4; y++) { // Raised by 2 Y levels
                BlockPos solutionPos = puzzleCenter.offset(x, y, -3); // 3 blocks towards players from wall

                // Create stone platform for placing blocks
                level.setBlock(solutionPos, Blocks.STONE.defaultBlockState(), 3);

                // Store original block state for cleanup
                originalBlocks.put(solutionPos, level.getBlockState(solutionPos));

                // Add to target pattern map (this is where players will place blocks)
                targetPattern.put(solutionPos, Blocks.AIR); // Will be filled with correct colors later
            }
        }

        // Generate random target pattern (3x3 vertical grid)
        // This will be displayed on the wall and players must recreate it on the solution grid
        for (int x = -1; x <= 1; x++) {
            for (int y = 2; y <= 4; y++) { // Raised by 2 Y levels
                Block randomColor = COLOR_BLOCKS[random.nextInt(COLOR_BLOCKS.length)];
                BlockPos solutionPos = puzzleCenter.offset(x, y, -3); // Solution grid position
                targetPattern.put(solutionPos, randomColor); // Store what should be placed here
            }
        }

        // Gold border removed as requested
    }

    private void givePlayerBlocks(ServerPlayer player) {
        // Give players the blocks they need for the 3x3 puzzle
        for (Block colorBlock : COLOR_BLOCKS) {
            player.getInventory().add(new net.minecraft.world.item.ItemStack(colorBlock.asItem(), 9)); // 9 blocks of each color
        }

        // Show message about blocks
        player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
            net.minecraft.network.chat.Component.literal("§a§lYou received colored blocks! Use them to recreate the pattern!")));
    }


    

    
    private void showTargetPattern(MinecraftServer server) {
        ServerLevel level = server.overworld();

        // Show the target pattern for 15 seconds
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§a§lMEMORIZE THE PATTERN! §7Look at the stone grid! 15 seconds...")));
        }

        // Display the pattern on the WHITE BACKWALL (behind the stone wall)
        // This way players can see the pattern they need to recreate on the white background
        for (Map.Entry<BlockPos, Block> entry : targetPattern.entrySet()) {
            BlockPos stonePos = entry.getKey(); // This is the stone block position
            // Calculate corresponding position on the white backwall
            int relativeX = stonePos.getX() - puzzleCenter.getX();
            int relativeY = stonePos.getY() - puzzleCenter.getY();
            BlockPos backwallPos = puzzleCenter.offset(relativeX, relativeY, -4); // On the white backwall (behind stone solution grid)
            Block targetBlock = entry.getValue();

            // Show the pattern on the white backwall
            level.setBlock(backwallPos, targetBlock.defaultBlockState(), 3);
        }

        // Play pattern reveal sound
        level.playSound(null, puzzleCenter, SoundEvents.ANVIL_LAND, SoundSource.BLOCKS, 2.0f, 1.0f);

        // Show countdown during memorization with wall grid instructions
        for (int i = 1; i <= 3; i++) {
            final int timeLeft = 15 - (i * 5);
            scheduler.schedule(() -> {
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                        net.minecraft.network.chat.Component.literal("§e§l" + timeLeft + " seconds left! §7Study the pattern!")));
                }
            }, i * 5L, TimeUnit.SECONDS);
        }

        // Hide pattern after 15 seconds and start the challenge
        scheduler.schedule(() -> {
            // Remove the pattern from the white backwall (restore to white wool)
            for (Map.Entry<BlockPos, Block> entry : targetPattern.entrySet()) {
                BlockPos stonePos = entry.getKey();
                // Calculate corresponding position on the white backwall
                int relativeX = stonePos.getX() - puzzleCenter.getX();
                int relativeY = stonePos.getY() - puzzleCenter.getY();
                BlockPos backwallPos = puzzleCenter.offset(relativeX, relativeY, -4);

                // Restore the white backwall
                level.setBlock(backwallPos, Blocks.WHITE_WOOL.defaultBlockState(), 3);
            }

            // Start the challenge
            startChallenge(server);

        }, 15L, TimeUnit.SECONDS);
    }
    
    private void startChallenge(MinecraftServer server) {
        // Show challenge message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§c§lRECREATE THE PATTERN! §7Place blocks on the STONE grid in front of you! 20 seconds or BOOM!")));
        }
        
        // Play urgent sound
        ServerLevel level = server.overworld();
        level.playSound(null, puzzleCenter, SoundEvents.FIREWORK_ROCKET_LAUNCH, SoundSource.BLOCKS, 2.0f, 2.0f);
        
        // Start countdown warnings
        for (int i = 1; i <= 4; i++) {
            final int timeLeft = 20 - (i * 5);
            scheduler.schedule(() -> {
                if (puzzleActive) {
                    for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                        player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                            net.minecraft.network.chat.Component.literal("§c§l" + timeLeft + " SECONDS LEFT! §7Complete the pattern!")));
                    }
                    level.playSound(null, puzzleCenter, SoundEvents.GENERIC_EXPLODE, SoundSource.BLOCKS, 1.0f, 0.5f);
                }
            }, i * 5L, TimeUnit.SECONDS);
        }
        
        // Give players 5 seconds to start placing blocks, then check every 3 seconds
        for (int i = 1; i <= 5; i++) { // 5 checks over 15 seconds (after 5-second grace period)
            scheduler.schedule(() -> {
                if (puzzleActive) {
                    checkSolution(server);
                }
            }, (5 + i * 3L), TimeUnit.SECONDS); // Start checking at 8s, 11s, 14s, 17s, 20s
        }
        
        // Final explosion if not solved in 20 seconds
        scheduler.schedule(() -> {
            if (puzzleActive) {
                explodeEveryone(server);
            }
        }, 20L, TimeUnit.SECONDS);
    }
    
    private void checkSolution(MinecraftServer server) {
        ServerLevel level = server.overworld();

        // Check if current pattern matches target pattern
        int correctBlocks = 0;
        int totalBlocks = targetPattern.size();

        for (Map.Entry<BlockPos, Block> entry : targetPattern.entrySet()) {
            BlockPos stonePos = entry.getKey(); // This is the stone block position
            // Calculate corresponding position on the white backwall (where players are actually placing blocks)
            int relativeX = stonePos.getX() - puzzleCenter.getX();
            int relativeY = stonePos.getY() - puzzleCenter.getY();
            BlockPos checkPos = puzzleCenter.offset(relativeX, relativeY, -4); // Check the white wool wall position
            Block targetBlock = entry.getValue();
            BlockState currentState = level.getBlockState(checkPos);
            Block currentBlock = currentState.getBlock();

            // Debug: Show what we're checking
            System.out.println("Checking white wool position " + checkPos + " (corresponding to stone at " + stonePos + "): Expected " + targetBlock + ", Found " + currentBlock);

            if (currentBlock == targetBlock) {
                correctBlocks++;
            }
        }

        // Debug: Show progress
        System.out.println("Pattern check: " + correctBlocks + "/" + totalBlocks + " correct blocks");

        // Only succeed if ALL blocks are correct (don't fail on partial completion)
        if (correctBlocks == totalBlocks) {
            // Success!
            puzzleActive = false;

            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§a§lPUZZLE SOLVED! §7Excellent teamwork!")));
            }

            // Play success sound
            level.playSound(null, puzzleCenter, SoundEvents.PLAYER_LEVELUP, SoundSource.BLOCKS, 2.0f, 1.0f);

            // Teleport players back to original positions after 3 seconds
            scheduler.schedule(() -> {
                teleportPlayersBack(server);

                // Clean up after teleporting
                scheduler.schedule(() -> {
                    cleanupPuzzle(level);
                }, 2L, TimeUnit.SECONDS);
            }, 3L, TimeUnit.SECONDS);
        } else {
            // Show progress to players
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§e§lProgress: " + correctBlocks + "/" + totalBlocks + " blocks correct")));
            }
        }
    }
    
    private void explodeEveryone(MinecraftServer server) {
        puzzleActive = false;
        
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§c§lTIME'S UP! §4§lTEAMWORK FAILED!")));
            
            // Create explosion at each player's location
            ServerLevel level = player.serverLevel();
            BlockPos playerPos = player.blockPosition();
            
            // Visual explosion (no block damage)
            level.explode(null, playerPos.getX(), playerPos.getY(), playerPos.getZ(), 
                         3.0f, false, net.minecraft.world.level.Level.ExplosionInteraction.NONE);
            
            // Deal 3 hearts of damage
            player.hurt(level.damageSources().explosion(null, null), 6.0f);
        }
        
        // Teleport players back to original positions after 3 seconds
        scheduler.schedule(() -> {
            teleportPlayersBack(server);

            // Clean up after teleporting
            scheduler.schedule(() -> {
                cleanupPuzzle(server.overworld());
            }, 2L, TimeUnit.SECONDS);
        }, 3L, TimeUnit.SECONDS);
    }

    private void teleportPlayersBack(MinecraftServer server) {
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            if (player.isAlive() && originalPlayerPositions.containsKey(player)) {
                BlockPos originalPos = originalPlayerPositions.get(player);
                ServerLevel playerLevel = player.serverLevel();

                // Teleport back to the exact original ground position where they were first teleported from
                player.teleportTo(playerLevel, originalPos.getX() + 0.5, originalPos.getY(), originalPos.getZ() + 0.5, player.getYRot(), player.getXRot());

                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§7§lYou have been returned to your original location.")));
            }
        }
    }

    private void cleanupPuzzle(ServerLevel level) {
        // Resume the main event timer
        // TODO: Add resume functionality to RandomEventManager

        // Remove solution grid (stone blocks and any blocks placed above them)
        for (BlockPos stonePos : targetPattern.keySet()) {
            // Clear the stone block
            level.setBlock(stonePos, Blocks.AIR.defaultBlockState(), 3);
            // Clear any blocks placed above the stone
            level.setBlock(stonePos.above(), Blocks.AIR.defaultBlockState(), 3);
        }

        // Remove player platform (7x7 obsidian platform)
        for (int x = -3; x <= 3; x++) {
            for (int z = -3; z <= 3; z++) {
                BlockPos pos = playerPlatform.offset(x, 0, z);
                level.setBlock(pos, Blocks.AIR.defaultBlockState(), 3);
            }
        }

        // Stone brick wall cleanup removed since we no longer create stone brick walls

        // Remove white backwall
        for (int x = -1; x <= 1; x++) {
            for (int y = 2; y <= 4; y++) { // Raised by 2 Y levels
                BlockPos backwallPos = puzzleCenter.offset(x, y, -4); // Behind stone solution grid
                level.setBlock(backwallPos, Blocks.AIR.defaultBlockState(), 3);
            }
        }

        originalBlocks.clear();
        targetPattern.clear();
        originalPlayerPositions.clear();
    }
}
