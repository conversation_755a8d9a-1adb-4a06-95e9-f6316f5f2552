package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.item.ItemEntity;
import net.minecraft.world.entity.item.PrimedTnt;
import net.minecraft.world.entity.npc.Villager;
import net.minecraft.world.entity.npc.VillagerProfession;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

public class VillagerTakeoverEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();

    
    @Override
    public String getId() {
        return "villager_takeover";
    }
    
    @Override
    public String getName() {
        return "§2§lVillager Takeover!";
    }
    
    @Override
    public String getDescription() {
        return "20 villagers spawn, throw diamonds/iron, follow players, then explode after 20 seconds";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        List<Villager> spawnedVillagers = new ArrayList<>();
        
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            ServerLevel level = player.serverLevel();
            BlockPos playerPos = player.blockPosition();
            
            // Spawn 20 villagers around each player
            for (int i = 0; i < 20; i++) {
                int x = playerPos.getX() + ThreadLocalRandom.current().nextInt(20) - 10;
                int z = playerPos.getZ() + ThreadLocalRandom.current().nextInt(20) - 10;
                int y = level.getHeight(net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING_NO_LEAVES, x, z);
                
                BlockPos spawnPos = new BlockPos(x, y, z);
                
                // Make sure the spawn position is safe
                if (level.getBlockState(spawnPos).isAir() && 
                    level.getBlockState(spawnPos.above()).isAir() &&
                    !level.getBlockState(spawnPos.below()).isAir()) {
                    
                    Villager villager = EntityType.VILLAGER.create(level);
                    if (villager != null) {
                        villager.moveTo(spawnPos.getX() + 0.5, spawnPos.getY(), spawnPos.getZ() + 0.5);
                        
                        // Give villager a random profession for variety
                        VillagerProfession[] professions = {
                            VillagerProfession.FARMER, VillagerProfession.LIBRARIAN,
                            VillagerProfession.CLERIC, VillagerProfession.BUTCHER,
                            VillagerProfession.ARMORER, VillagerProfession.NITWIT
                        };
                        villager.setVillagerData(villager.getVillagerData()
                            .setProfession(professions[ThreadLocalRandom.current().nextInt(professions.length)]));
                        
                        // Make villager faster to follow players better
                        villager.getAttribute(net.minecraft.world.entity.ai.attributes.Attributes.MOVEMENT_SPEED)
                            .setBaseValue(0.6); // Faster than normal
                        
                        level.addFreshEntity(villager);
                        spawnedVillagers.add(villager);
                    }
                }
            }
        }
        
        // Make villagers follow players and throw items for 20 seconds
        for (int i = 0; i < 20; i++) {
            scheduler.schedule(() -> {
                for (Villager villager : spawnedVillagers) {
                    if (villager.isAlive()) {
                        ServerLevel level = (ServerLevel) villager.level();

                        // Find nearest player
                        ServerPlayer nearestPlayer = null;
                        double nearestDistance = Double.MAX_VALUE;

                        for (ServerPlayer player : level.getServer().getPlayerList().getPlayers()) {
                            if (player.serverLevel() == level) {
                                double distance = villager.distanceToSqr(player);
                                if (distance < nearestDistance) {
                                    nearestDistance = distance;
                                    nearestPlayer = player;
                                }
                            }
                        }

                        // Follow the nearest player
                        if (nearestPlayer != null && nearestDistance < 400) { // Within 20 blocks
                            net.minecraft.world.phys.Vec3 direction = nearestPlayer.position()
                                .subtract(villager.position()).normalize();
                            villager.setDeltaMovement(direction.scale(0.4));

                            // Throw diamonds and iron ingots at players!
                            if (ThreadLocalRandom.current().nextInt(10) == 0) { // 10% chance per second
                                throwItemAtPlayer(villager, nearestPlayer, level);
                            }
                        }
                    }
                }
            }, i * 1L, TimeUnit.SECONDS);
        }
        
        // After 15 seconds, show warning message
        scheduler.schedule(() -> {
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§c§lYou might want to run...")));
            }
        }, 15L, TimeUnit.SECONDS);

        // After 20 seconds, turn all villagers into TNT
        scheduler.schedule(() -> {
            for (Villager villager : spawnedVillagers) {
                if (villager.isAlive()) {
                    ServerLevel level = (ServerLevel) villager.level();
                    BlockPos villagerPos = villager.blockPosition();

                    // Remove the villager
                    villager.remove(net.minecraft.world.entity.Entity.RemovalReason.DISCARDED);

                    // Spawn TNT at villager's location
                    PrimedTnt tnt = EntityType.TNT.create(level);
                    if (tnt != null) {
                        tnt.moveTo(villagerPos.getX() + 0.5, villagerPos.getY(), villagerPos.getZ() + 0.5);
                        tnt.setFuse(40 + ThreadLocalRandom.current().nextInt(20)); // 2-3 seconds fuse
                        level.addFreshEntity(tnt);
                    }
                }
            }
        }, 20L, TimeUnit.SECONDS);
    }

    private void throwItemAtPlayer(Villager villager, ServerPlayer player, ServerLevel level) {
        // Choose random valuable item to throw
        ItemStack itemToThrow;
        if (ThreadLocalRandom.current().nextInt(2) == 0) {
            itemToThrow = new ItemStack(Items.DIAMOND, 1 + ThreadLocalRandom.current().nextInt(3)); // 1-3 diamonds
        } else {
            itemToThrow = new ItemStack(Items.IRON_INGOT, 2 + ThreadLocalRandom.current().nextInt(4)); // 2-5 iron ingots
        }

        // Create item entity
        ItemEntity itemEntity = new ItemEntity(level,
            villager.getX(), villager.getY() + 1, villager.getZ(), itemToThrow);

        // Calculate throw direction towards player
        net.minecraft.world.phys.Vec3 direction = player.position()
            .subtract(villager.position()).normalize();

        // Add some upward arc and speed
        itemEntity.setDeltaMovement(
            direction.x * 0.5 + (ThreadLocalRandom.current().nextDouble() - 0.5) * 0.2, // X with some randomness
            0.3 + ThreadLocalRandom.current().nextDouble() * 0.2, // Upward arc
            direction.z * 0.5 + (ThreadLocalRandom.current().nextDouble() - 0.5) * 0.2  // Z with some randomness
        );

        level.addFreshEntity(itemEntity);
    }
}
