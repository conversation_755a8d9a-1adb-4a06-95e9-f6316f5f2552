package com.randomevents.events;

import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class TrustFallEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Map<ServerPlayer, Double> launchHeights = new HashMap<>();
    
    @Override
    public String getId() {
        return "trust_fall";
    }
    
    @Override
    public String getName() {
        return "§b§lTrust Fall!";
    }
    
    @Override
    public String getDescription() {
        return "Launch the player into the air and give them Slow Falling";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        launchHeights.clear();

        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            // Launch player VERY high into the air
            player.setDeltaMovement(player.getDeltaMovement().add(0, 4.0, 0)); // Much stronger upward velocity
            player.hurtMarked = true; // Force velocity update

            // Store the launch height to calculate when they're halfway down
            launchHeights.put(player, player.getY());

            // Start monitoring the player's fall
            monitorPlayerFall(player);
        }
    }

    private void monitorPlayerFall(ServerPlayer player) {
        // Check every 0.2 seconds for up to 30 seconds
        for (int i = 0; i < 150; i++) { // 150 checks over 30 seconds
            scheduler.schedule(() -> {
                if (player.isAlive() && launchHeights.containsKey(player)) {
                    double launchHeight = launchHeights.get(player);
                    double currentHeight = player.getY();
                    double maxHeight = launchHeight + 40; // Estimate max height reached

                    // Check if player is falling and halfway down from max height
                    if (currentHeight < maxHeight && currentHeight > launchHeight &&
                        player.getDeltaMovement().y < 0) { // Player is falling

                        double fallDistance = maxHeight - currentHeight;
                        double totalFallDistance = maxHeight - launchHeight;

                        // If player has fallen halfway or more, give slow falling
                        if (fallDistance >= totalFallDistance * 0.5) {
                            // Give slow falling effect for remaining fall
                            player.addEffect(new MobEffectInstance(MobEffects.SLOW_FALLING, 600, 0, false, false));

                            // Remove from monitoring
                            launchHeights.remove(player);
                        }
                    }

                    // Remove from monitoring if player is back on ground
                    if (player.onGround()) {
                        launchHeights.remove(player);
                    }
                }
            }, i * 200L, TimeUnit.MILLISECONDS); // Every 0.2 seconds
        }

        // Cleanup after 30 seconds
        scheduler.schedule(() -> {
            launchHeights.remove(player);
        }, 30L, TimeUnit.SECONDS);
    }
}
