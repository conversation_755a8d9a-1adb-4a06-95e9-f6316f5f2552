package com.randomevents.events;

import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class TrustFallEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    
    @Override
    public String getId() {
        return "trust_fall";
    }
    
    @Override
    public String getName() {
        return "§b§lTrust Fall!";
    }
    
    @Override
    public String getDescription() {
        return "Launch the player into the air and give them Slow Falling";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            // Launch player VERY high into the air
            player.setDeltaMovement(player.getDeltaMovement().add(0, 4.0, 0)); // Much stronger upward velocity
            player.hurtMarked = true; // Force velocity update

            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§b§lTRUST FALL! §7You've been launched into the air!")));
        }

        // Give all players Slow Falling after 4 seconds
        scheduler.schedule(() -> {
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                if (player.isAlive()) {
                    // Give slow falling effect for 30 seconds (plenty of time to land safely)
                    player.addEffect(new MobEffectInstance(MobEffects.SLOW_FALLING, 600, 0, false, false));

                    // Show message
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                        net.minecraft.network.chat.Component.literal("§b§lSLOW FALLING ACTIVATED! §7Trust the fall...")));
                }
            }
        }, 4L, TimeUnit.SECONDS);
    }


}
