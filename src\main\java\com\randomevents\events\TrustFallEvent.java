package com.randomevents.events;

import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class TrustFallEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Map<ServerPlayer, Double> launchHeights = new HashMap<>();
    private final Map<ServerPlayer, Double> maxHeights = new HashMap<>();
    private final Map<ServerPlayer, Boolean> slowFallingGiven = new HashMap<>();
    
    @Override
    public String getId() {
        return "trust_fall";
    }
    
    @Override
    public String getName() {
        return "§b§lTrust Fall!";
    }
    
    @Override
    public String getDescription() {
        return "Launch the player into the air and give them Slow Falling";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        launchHeights.clear();
        maxHeights.clear();
        slowFallingGiven.clear();

        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            // Launch player VERY high into the air
            player.setDeltaMovement(player.getDeltaMovement().add(0, 4.0, 0)); // Much stronger upward velocity
            player.hurtMarked = true; // Force velocity update

            // Store the launch height and initialize tracking
            double startHeight = player.getY();
            launchHeights.put(player, startHeight);
            maxHeights.put(player, startHeight); // Will be updated as player rises
            slowFallingGiven.put(player, false);

            System.out.println("Trust Fall: Launched " + player.getName().getString() + " from height " + startHeight);

            // Start monitoring the player's fall
            monitorPlayerFall(player);
        }
    }

    private void monitorPlayerFall(ServerPlayer player) {
        // Check every 0.2 seconds for up to 30 seconds
        for (int i = 0; i < 150; i++) { // 150 checks over 30 seconds
            scheduler.schedule(() -> {
                if (player.isAlive() && launchHeights.containsKey(player)) {
                    double launchHeight = launchHeights.get(player);
                    double currentHeight = player.getY();
                    double currentMaxHeight = maxHeights.get(player);
                    boolean alreadyGivenSlowFalling = slowFallingGiven.get(player);

                    // Update max height if player is still rising
                    if (currentHeight > currentMaxHeight) {
                        maxHeights.put(player, currentHeight);
                        currentMaxHeight = currentHeight;
                        System.out.println("Trust Fall: " + player.getName().getString() + " reached new max height: " + currentMaxHeight);
                    }

                    // Check if player is falling and hasn't received slow falling yet
                    if (!alreadyGivenSlowFalling && player.getDeltaMovement().y < 0) { // Player is falling
                        double totalFallDistance = currentMaxHeight - launchHeight;
                        double currentFallDistance = currentMaxHeight - currentHeight;
                        double fallPercentage = totalFallDistance > 0 ? (currentFallDistance / totalFallDistance) : 0;

                        // Debug logging
                        System.out.println("Trust Fall Debug - Player: " + player.getName().getString() +
                                         ", Launch: " + launchHeight + ", Max: " + currentMaxHeight +
                                         ", Current: " + currentHeight + ", Fall %: " + (fallPercentage * 100) + "%");

                        // If player has fallen 30% or more from their max height, give slow falling (lowered threshold)
                        if (fallPercentage >= 0.3 && totalFallDistance > 5) { // Also ensure meaningful fall distance
                            // Give slow falling effect for remaining fall
                            player.addEffect(new MobEffectInstance(MobEffects.SLOW_FALLING, 600, 0, false, false));

                            // Show message
                            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                                net.minecraft.network.chat.Component.literal("§b§lSLOW FALLING ACTIVATED! §7Trust the fall...")));

                            // Mark as given
                            slowFallingGiven.put(player, true);

                            System.out.println("Trust Fall: Slow falling given to " + player.getName().getString() +
                                             " at " + (fallPercentage * 100) + "% fall completion");
                        }
                    }

                    // Remove from monitoring if player is back on ground
                    if (player.onGround()) {
                        launchHeights.remove(player);
                        maxHeights.remove(player);
                        slowFallingGiven.remove(player);
                    }
                }
            }, i * 200L, TimeUnit.MILLISECONDS); // Every 0.2 seconds
        }

        // Cleanup after 30 seconds
        scheduler.schedule(() -> {
            launchHeights.remove(player);
            maxHeights.remove(player);
            slowFallingGiven.remove(player);
        }, 30L, TimeUnit.SECONDS);
    }
}
