package com.randomevents.events;

import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;

public class TrustFallEvent extends RandomEvent {
    
    @Override
    public String getId() {
        return "trust_fall";
    }
    
    @Override
    public String getName() {
        return "§b§lTrust Fall!";
    }
    
    @Override
    public String getDescription() {
        return "Launch the player into the air and give them Slow Falling";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            // Launch player into the air
            player.setDeltaMovement(player.getDeltaMovement().add(0, 2.0, 0)); // Strong upward velocity
            player.hurtMarked = true; // Force velocity update
            
            // Give slow falling effect for 30 seconds
            player.addEffect(new MobEffectInstance(MobEffects.SLOW_FALLING, 600, 0, false, false)); // 30 seconds
            
            // Show message
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§b§lTrust fall! §7You're launched up with slow falling!")));
        }
    }
}
